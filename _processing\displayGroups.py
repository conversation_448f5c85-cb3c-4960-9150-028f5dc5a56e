from qgis.PyQt.QtCore import QCoreApplication
from qgis.core import (QgsProcessing,
                       QgsFeatureSink,
                       QgsProcessingException,
                       QgsProcessingAlgorithm,
                       QgsProcessingParameterFeatureSource,
                       QgsProcessingParameterFeatureSink,
                       QgsProcessingParameterNumber,
                       QgsProcessingParameterString,
                       QgsProcessingParameterBoolean,
                       QgsProcessingParameterFolderDestination,
                       QgsProcessingParameterMultipleLayers,
                       QgsProcessingParameterVectorLayer,
                       QgsProcessingParameterMapLayer,
                       QgsCoordinateReferenceSystem,
                       QgsProcessingFeatureSourceDefinition,
                       QgsExpressionContext,
                       QgsExpressionContextUtils,
                       QgsWkbTypes,
                       QgsProject,
                       QgsFeature,
                       QgsField,
                       QgsVectorFileWriter,
                       QgsVectorLayer,
                       QgsFeatureRequest,
                       QgsProperty,
                       QgsExpression,
                       NULL)
                    
from qgis import processing
from qgis import os
from qgis.PyQt.QtWidgets import QAction, QMessageBox, QFileDialog
from qgis.utils import iface
from qgis.PyQt.QtCore import QVariant
import layer2kmz.layer2kmz as exporter
import time


# IMPORT COMMON SCRIPTS
import importlib.util
import sys
module_path = os.path.join(os.path.dirname(QgsProject.instance().fileName()), '_common','_processing','common.py')
spec = importlib.util.spec_from_file_location("common", module_path)
common = importlib.util.module_from_spec(spec)
sys.modules["common"] = common
spec.loader.exec_module(common)

 
class display_groups(QgsProcessingAlgorithm):

    BACKLOG = 'BACKLOG'
    HIRF = 'HIRF'
    ALLSEGMENTS = 'ALLSEGMENTS'
    DEBUG = 'DEBUG'
    
    message = (
        ""
    )
    
    def tr(self, string):
        return QCoreApplication.translate('Processing', string)
    def createInstance(self):
        return display_groups()
    def name(self):
        return 'display_groups'
    def displayName(self):
        return self.tr('Display Groups')
    def group(self):
        return self.tr('Jedsy scripts')
    def groupId(self):
        return 'jedsyscripts'
    def shortHelpString(self):
        return self.tr(self.message)
    def flags(self):
        return QgsProcessingAlgorithm.FlagNoThreading
    
    def initAlgorithm(self, config=None):
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.BACKLOG,
                self.tr('Display backlog Items'),
                defaultValue = bool(0)
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.HIRF,
                self.tr('Display HIRF only on Ground Risk layer'),
                defaultValue = bool(0)
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.ALLSEGMENTS,
                self.tr('Hide all layers, show all segments layers'),
                defaultValue = bool(0),
                optional=True
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.DEBUG,
                self.tr('Debug mode'),
                optional = True,
                defaultValue = False
            )
        )        

    def processAlgorithm(self, parameters, context, feedback):
        # Retrieve current project instance
        project = QgsProject.instance()
        
        debug = self.parameterAsBool(parameters, self.DEBUG, context)

        # Get the current project variables
        project_scope = QgsExpressionContextUtils.projectScope(project)
        variable_names = project_scope.variableNames()

        # Check and delete the variables if they exist
        for var_name in ['backlog', 'hirf']:
            if var_name in variable_names:
                # Print the variable name, value, and type
                current_value = project_scope.variable(var_name)
                if debug: print(f"Existing variable '{var_name}' -> Value: {current_value}, Type: {type(current_value)}")
                # Delete the variable
                QgsExpressionContextUtils.removeProjectVariable(project, var_name)
                if debug: print(f"Variable '{var_name}' has been deleted.")

        # Set the new project variables
        backlog_value = parameters[self.BACKLOG]
        hirf_value = parameters[self.HIRF]

        QgsExpressionContextUtils.setProjectVariable(project, 'backlog', backlog_value)
        QgsExpressionContextUtils.setProjectVariable(project, 'hirf', hirf_value)

        if debug: print(f"Set project variable 'backlog' as {backlog_value} (Type: {type(backlog_value)})")
        if debug: print(f"Set project variable 'hirf' as {hirf_value} (Type: {type(hirf_value)})")

        
        # Print all project variables (optional debugging) - doesn't work, it takes time to update the variables?
        project_scope = QgsExpressionContextUtils.projectScope(project)
        variable_names = project_scope.variableNames()        
        if debug: print("\nUpdated Project Variables:")
        for name in project_scope.variableNames():
            if name == 'backlog' or name == 'hirf':
                value = project_scope.variable(name)
                if debug: print(f"{name}: {value} (Type: {type(value)})")
        
        

        
        if parameters[self.ALLSEGMENTS]:
            # Get the root of the project layer tree
            root = QgsProject.instance().layerTreeRoot()
            groups_to_show = set()  # Store group names to be made visible

            # Iterate through all layers in the project
            for layer in QgsProject.instance().mapLayers().values():
                node = root.findLayer(layer.id())
                if node:
                    # Hide all layers
                    node.setItemVisibilityChecked(False)
                    
                    # Show layers where the second part of the name (split by '_') is 'Segments'
                    name_parts = layer.name().split('_')
                    if (len(name_parts) > 1 and name_parts[1] == "Segments") or layer.name() == "OpenStreetMap":
                        node.setItemVisibilityChecked(True)

                        # Get the parent group of the layer
                        parent = node.parent()
                        while parent is not None and parent != root:
                            groups_to_show.add(parent)
                            parent = parent.parent()

            # Make the relevant groups visible
            for group in groups_to_show:
                group.setItemVisibilityChecked(True)

        
        
        # Redraw the map canvas
        canvas = iface.mapCanvas()  # Get the reference to the current map canvas
        canvas.refreshAllLayers()  # Refresh all layers        

        return {}