from qgis.PyQt.QtCore import(
    QCoreApplication,
    QVariant)
from qgis.core import (QgsProcessing,
                       QgsFeatureSink,
                       QgsProcessingException,
                       QgsProcessingAlgorithm,
                       QgsProcessingParameterMapLayer,
                       QgsProcessingParameterEnum,
                       QgsProcessingParameterString,
                       QgsProcessingParameterMultipleLayers,
                       QgsProcessingParameterBoolean,
                       QgsProcessingParameterNumber,
                       QgsExpressionContext,
                       QgsExpressionContextUtils,
                       QgsProject,
                       QgsVectorFileWriter,
                       QgsVectorLayer,
                       QgsFeatureRequest,
                       QgsField,
                       QgsVectorDataProvider,
                       QgsCoordinateTransform,
                       QgsCoordinateReferenceSystem,
                       QgsGeometry,
                       QgsWkbTypes,
                       QgsExpression,
                       QgsFields,
                       QgsFeature,
                       NULL)

from qgis import processing
from qgis import os
from qgis.PyQt.QtWidgets import QAction, QMessageBox, QFileDialog
from qgis.utils import iface
import math

# IMPORT COMMON SCRIPTS
import importlib.util
import sys
module_path = os.path.join(os.path.dirname(QgsProject.instance().fileName()), '_common','_processing','common.py')
spec = importlib.util.spec_from_file_location("common", module_path)
common = importlib.util.module_from_spec(spec)
sys.modules["common"] = common
spec.loader.exec_module(common)


class genericTools(QgsProcessingAlgorithm):

    LAYERS = 'LAYERS'
    GEOM = 'GEOM'
    ID = 'ID'
    STYLE = 'STYLE'
    DEBUG = 'DEBUG'
    RATIO = 'RATIO'
    SAVESTYLE = 'SAVESTYLE'
    RALLYNAMES = 'RALLYNAMES'
    SORT = 'SORT'
    MAKERALLY = 'MAKERALLY'
    MAKERALLYDISTANCE = 'MAKERALLYDISTANCE'
    DELTAS = 'DELTAS'
    FIELDS = 'FIELDS'
    
    def tr(self, string):
        return QCoreApplication.translate('Processing', string)
    def createInstance(self):
        return genericTools()
    def name(self):
        return 'genericTools'
    def displayName(self):
        return self.tr('Clean layers')
    def group(self):
        return self.tr('Jedsy scripts')
    def groupId(self):
        return 'jedsyscripts'
    def shortHelpString(self):
        return self.tr("General Tools")
    def flags(self):
        return QgsProcessingAlgorithm.FlagNoThreading
    
    def initAlgorithm(self, config=None):
        self.addParameter(
            QgsProcessingParameterMultipleLayers(
                name = self.LAYERS,
                description='input layers',
                defaultValue = iface.layerTreeView().selectedLayers()
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                name = self.GEOM,
                description= 'Remove points with missing geometry',
                defaultValue = True,
                optional = True
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                name = self.FIELDS,
                description= 'Clean bad fields (Name, temp)',
                optional = True,
                defaultValue = False
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                name = self.ID,
                description= 'Assign IDs where missing',
                defaultValue = bool(1)
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                name = self.STYLE,
                description= 'Style selected layers',
                defaultValue = bool(1)
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.RATIO,
                self.tr('Calculate ratio'),
                defaultValue = False,
                optional = True
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.SAVESTYLE,
                self.tr('Save Style'),
                defaultValue = False,
                optional = True
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.DELTAS,
                self.tr('Set default deltas for fp layer'),
                defaultValue = True,
                optional = False
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                name = self.MAKERALLY,
                description='Make Rally point draft',
                defaultValue = bool(0),
                optional = True
            )
        )
        self.addParameter(
            QgsProcessingParameterNumber(
                name = self.MAKERALLYDISTANCE,
                description = 'Meters between Rally points',
                type = QgsProcessingParameterNumber.Double,
                defaultValue = 6000,
                optional = True
            )
        )     
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.RALLYNAMES,
                self.tr('Rename selected Rally points'),
                defaultValue = bool(0),
                optional = bool(1)
            )
        )
        self.addParameter(
            QgsProcessingParameterEnum(
                name = self.SORT,
                description='Rename rally points by',
                options = ['Latitude','Longitude'],
                defaultValue =  0,
                optional = True
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.DEBUG,
                self.tr('Debug mode'),
                defaultValue = bool(0),
                optional = bool(1)
            )
        )       

    def processAlgorithm(self, parameters, context, feedback):
        # CLEAN POINTS
        def cleanPts(layer):
            layer.startEditing()
            for feature in layer.getFeatures():
                if feature.geometry() is None or feature.geometry().isEmpty():
                    # feedback.pushInfo(f"Deleting feature {feature.id()} with empty geometry.")
                    layer.dataProvider().deleteFeatures([feature.id()])
            layer.commitChanges()
        
        # CLEAN FIELDs
        def clean_fields(layer, debug=False):

            fields = layer.fields()
            field_names = fields.names()
            
            field_names_to_check = ["Name","notes"]
            fields_to_delete = ["temp"]
            
            for field_name in field_names:

                field = fields[field_names.index(field_name)]
                
                # make field names lower case and set len to unlimited
                if field_name in field_names_to_check:
                    if debug: print(f"\nChecking {layer.name()} - {field.name()} - len {field.length()}")

                    old_name = field_name
                    temp_name = field_name + '_tmp'
                    final_name = field_name.lower()
                
                    if debug: print(f"Regenerating field old_name {old_name} to final_name {final_name}")

                    old_index = layer.fields().indexOf(old_name)
                    old_field = layer.fields()[old_index]

                    if not layer.isEditable():
                        layer.startEditing()

                    # Step 1: Add temporary field
                    # if not layer.addAttribute(QgsField(temp_name, old_field.type(), old_field.typeName(), old_field.length(), old_field.precision())):
                    if not layer.addAttribute(QgsField(temp_name, old_field.type(), old_field.typeName(), 0, old_field.precision())):
                        if debug: print("Failed to add temporary field.")
                        layer.rollBack()
                        return
                    layer.updateFields()
                    temp_index = layer.fields().indexOf(temp_name)

                    # Step 2: Copy data to temporary field
                    for feature in layer.getFeatures():
                        layer.changeAttributeValue(feature.id(), temp_index, feature[old_index])

                    # Step 3: Delete original field "Name"
                    if not layer.deleteAttribute(old_index):
                        if debug: print("Failed to delete old field.")
                        layer.rollBack()
                        return
                    layer.updateFields()

                    # Step 4: Commit to finalize deletion of the old field
                    if not layer.commitChanges():
                        if debug: print("Failed to commit intermediate changes.")
                        return
                    if debug: print(f"Committed changes after deleting {old_name}")

                    # Step 5: Start new edit session
                    layer.startEditing()

                    # Step 6: Add final field
                    # if not layer.addAttribute(QgsField(final_name, old_field.type(), old_field.typeName(), old_field.length(), old_field.precision())):
                    if not layer.addAttribute(QgsField(final_name, old_field.type(), old_field.typeName(), 0, old_field.precision())):
                        if debug: print("Failed to add final field.")
                        layer.rollBack()
                        return
                    layer.updateFields()
                    final_index = layer.fields().indexOf(final_name)
                    temp_index = layer.fields().indexOf(temp_name)

                    # Step 7: Copy data from temp to final
                    for feature in layer.getFeatures():
                        layer.changeAttributeValue(feature.id(), final_index, feature[temp_index])

                    # Step 8: Delete temp field
                    if not layer.deleteAttribute(temp_index):
                        if debug: print("Failed to delete temporary field.")
                        layer.rollBack()
                        return
                    layer.updateFields()

                    # Step 9: Final commit
                    if not layer.commitChanges():
                        if debug: print("Final commit failed.")
                        layer.rollBack()
                    else:
                        if debug: print(f"Renamed '{old_name}' to '{final_name}' successfully and set len to 0")
                
                # Delete unwanted fields
                if field_name in fields_to_delete:
                    if not layer.isEditable():
                        layer.startEditing()
                    if debug: print(f"Deleting field {field_name}")
                    idx = fields.indexFromName(field_name)
                    layer.deleteAttribute(idx)
                    layer.updateFields()
                    layer.commitChanges()




        
        # CREATES THE FIELD IF MISSING
        def fieldCheck(layer,field_name):
            if field_name.lower() in [field.name().lower() for field in layer.fields()]:
                return
            else:
                # Define the field type and name
                field = QgsField(field_name, QVariant.Int)

                layer.startEditing()
                
                # Add the field
                if layer.dataProvider().addAttributes([field]):
                    layer.updateFields()  # Update the layer's schema to reflect the change
                    print(f"Field '{field_name}' added successfully.")
                else:
                    print(f"Failed to add field '{field_name}'.")
                layer.commitChanges()


        # RESET IDS
        def resetIds(layer, startFrom):
            maxId = 0
            # Find the maximum existing ID
            for feature in layer.getFeatures():
                feature_id = feature['id']
                if feature_id and feature_id != NULL:
                    feature_id = int(feature['id'])  # Convert string to int
                    if feature_id > maxId:
                        maxId = feature_id

            layer.startEditing()
            for feature in layer.getFeatures():
                feature_id = feature['id']
                if feature_id == NULL or feature_id == '':
                    feature.setAttribute('id', maxId + 1)
                    maxId += 1
                    layer.updateFeature(feature)
                else:
                    feature.setAttribute('id', int(feature['id']))  # Ensure correct conversion

            layer.commitChanges()

        # RALLY DRAFT
        def rally_draft(layer, debug=False, distance = 6000, ratio = 1):
            if debug: print(f"RALLY DRAFT {layer}")
            
            parts = layer.name().split('_')
            if len(parts) < 2 or parts[1] != 'fp':
                if debug: print(f"{layer.name()} is not a 'fp' layer, skipping")
                return

            if layer.geometryType() != QgsWkbTypes.LineGeometry:
                if debug: print(f"{layer.name()} is not a line layer, skipping")
                return

            # Filter features with status = 'Active'
            expr = QgsExpression("\"status\" = 'Active'")
            context = QgsExpressionContext()
            context.appendScopes(QgsExpressionContextUtils.globalProjectLayerScopes(layer))
            request = QgsFeatureRequest(expr, context)

            crs = layer.crs()
            point_fields = QgsFields()
            point_fields.append(QgsField('orig_id', QVariant.Int))
            point_fields.append(QgsField('distance', QVariant.Double))

            mem_layer = QgsVectorLayer(f"Point?crs={crs.authid()}", f"{layer.name().split('_')[0]}_RallyDraft", "memory")
            mem_provider = mem_layer.dataProvider()
            mem_provider.addAttributes(point_fields)
            mem_layer.updateFields()

            for feat in layer.getFeatures(request):
                geom = feat.geometry()
                if not geom or geom.isEmpty():
                    continue

                line = geom.constGet()
                if line is None or not hasattr(line, 'length'):
                    continue

                length = geom.length()
                step = distance * ratio  # meters
                current_dist = 0

                while current_dist < length:
                    point = geom.interpolate(current_dist).asPoint()
                    point_feat = QgsFeature()
                    point_feat.setGeometry(QgsGeometry.fromPointXY(point))
                    point_feat.setAttributes([feat.id(), current_dist])
                    mem_provider.addFeatures([point_feat])
                    current_dist += step

            mem_layer.updateExtents()
            QgsProject.instance().addMapLayer(mem_layer)
            
            # style the new layer
            try:
                common.style_layer(mem_layer, deleteFields=False, add_fields=False, debug=debug)
            except Exception as e:
                raise QgsProcessingException(f"ERROR: {e}")          
                
            if debug: print(f"Rally draft points created in layer: {mem_layer.name()}")

        
        def rally_names(layer, sort_by=0, debug=False):
            if debug: print(f"########## rally_names {layer.name()} sort by {sort_by}")

            if not layer.isEditable():
                layer.startEditing()

            # Determine the sorting key
            if sort_by == 0:
                sort_key = lambda f: -f.geometry().asPoint().y()
            else:
                sort_key = lambda f: f.geometry().asPoint().x()

            expr = QgsExpression("descr = 'Rally'")
            context = QgsExpressionContext()
            context.appendScopes(QgsExpressionContextUtils.globalProjectLayerScopes(layer))
            request = QgsFeatureRequest(expr, context)

            if layer.selectedFeatureCount() > 0:
                features = [f for f in layer.getFeatures(request) if f.id() in layer.selectedFeatureIds()]
            else:
                features = [f for f in layer.getFeatures(request)]

            features.sort(key=sort_key)

            def index_to_letters(index):
                result = ''
                while index >= 0:
                    result = chr(index % 26 + 65) + result
                    index = index // 26 - 1
                return result

            field_index = layer.fields().indexOf('rallyletter')
            field_name_index = layer.fields().indexOf('name')
            if field_index == -1:
                if debug: print("Field 'rallyletter' does not exist.")
                return

            existing_letters = set(str(f['rallyletter']) for f in layer.getFeatures() if f['rallyletter'])

            index = 0
            renamed_count = 0
            for feature in features:
                suffix = index_to_letters(index)

                if suffix in existing_letters:
                    index += 1
                    continue

                if not feature['rallyletter']:
                    layer.changeAttributeValue(feature.id(), field_index, suffix)
                    layer.changeAttributeValue(feature.id(), field_name_index, layer.name().split("_")[0] + " Rally " + suffix)
                    index += 1
                    renamed_count += 1
                else:
                    index += 1  # Skip existing values but maintain indexing

            if not layer.commitChanges():
                if debug: print("Failed to commit changes")
            else:
                if debug: print("Changes committed successfully")

            if debug: print(f"Number of Rally points renamed: {renamed_count}")


        def default_deltas(layer, debug=False):
            if debug: print(f"\nDefault_deltas {layer}")

            if not layer.isEditable():
                layer.startEditing()

            fg_field_idx = layer.fields().indexFromName('fgaltdelta')
            cv_field_idx = layer.fields().indexFromName('cvaltdelta')

            if debug:
                print(f"fg_field_idx: {fg_field_idx}")
                print(f"cv_field_idx: {cv_field_idx}")

            for feature in layer.getFeatures():
                # attrs = {}
                val_fg = feature['fgaltdelta']
                val_cv = feature['cvaltdelta']

                if debug:
                    print(f"\nFeature ID: {feature.id()}")
                    print(f"fgaltdelta: {val_fg!r}, type: {type(val_fg)}")
                    print(f"cvaltdelta: {val_cv!r}, type: {type(val_cv)}")

                if fg_field_idx != -1 and val_fg == QVariant():
                    layer.changeAttributeValue(feature.id(), fg_field_idx, 20)
                    if debug: print(f" --> setting fgaltdelta = 25")

                if cv_field_idx != -1 and val_cv == QVariant():
                    layer.changeAttributeValue(feature.id(), cv_field_idx, 25)
                    if debug: print(f" --> setting cvaltdelta = 25")
                
            layer.commitChanges()


        
        # CONFIG
        debug = parameters.get('DEBUG', False)
        layers = self.parameterAsLayerList(parameters, self.LAYERS, context)
        projectpath = os.path.dirname(QgsProject.instance().fileName())
        stylespath = os.path.join(projectpath, '_common')
        root = QgsProject.instance().layerTreeRoot()
        if debug: print(f"########## Generic tools")
        
        for layer in layers:
            if type(layer) == QgsVectorLayer:
                # feedback.pushInfo(f"Cleaning {layer.name()}")
                if debug: print(f"Cleaning {layer}")
                fieldCheck(layer,'id')
                
                if self.parameterAsBool(parameters, self.GEOM, context):
                    cleanPts(layer)

                if self.parameterAsBool(parameters, self.FIELDS, context):
                    clean_fields(layer, debug)
                
                if self.parameterAsBool(parameters, self.ID, context):
                    try:
                        resetIds(layer,0)
                    except Exception as e:
                        raise QgsProcessingException(f"ERROR in resetIds(): {e}")
                
                if self.parameterAsBoolean(parameters, self.RATIO, context):
                    ratio_value = common.get_ratio(layer, debug=debug)
                    if ratio_value != -1:
                        feedback.pushInfo(f"{layer}\nRatio stored: {ratio_value}")
                    else:
                        raise QgsProcessingException(f"ERROR ratio invalid for: {layer}\nCheck the console for errors")
                
                if self.parameterAsBoolean(parameters, self.SAVESTYLE, context):
                    common.save_layer_style(layer,debug=debug)
                
                #if parameters['STYLE']:
                if self.parameterAsBool(parameters, self.STYLE, context):
                    try:
                        common.style_layer(layer, deleteFields=True, add_fields=True, debug=debug)
                    except Exception as e:
                        raise QgsProcessingException(f"ERROR in styleLayer(): {e}")

                if self.parameterAsBoolean(parameters, self.DELTAS, context):
                    if layer.name().split('_')[1] == "fp":  # flight path layer
                        default_deltas(layer, debug)

                if self.parameterAsBoolean(parameters, self.MAKERALLY, context):
                    distance = float(self.parameterAsDouble(parameters, self.MAKERALLYDISTANCE, context))
                    ratio = float(common.manage_layer_variable(layer,variable='ratio',write=False,debug=debug,ratio_value=1))
                    rally_draft(layer = layer, debug = debug, distance = distance, ratio = ratio)
 
                #if parameters['RALLYNAMES']:
                if self.parameterAsBoolean(parameters, self.RALLYNAMES, context):
                    try:
                        rally_names(layer, sort_by = self.parameterAsEnum(parameters, self.SORT, context), debug=debug)
                    except Exception as e:
                        raise QgsProcessingException(f"ERROR in rally_names(): {e}")

            else:
                feedback.pushInfo(f"WARNING: the layer {layer.name()} is not a valid vector layer")
        
        return {}