from qgis.PyQt.QtCore import(
    QCoreApplication,
    QVariant)
from qgis.core import (QgsProcessing,
                       QgsFeatureSink,
                       QgsProcessingException,
                       QgsProcessingAlgorithm,
                       QgsProcessingParameterMapLayer,
                       QgsProcessingParameterEnum,
                       QgsProcessingParameterString,
                       QgsProcessingParameterMultipleLayers,
                       QgsProcessingParameterBoolean,
                       QgsProcessingParameterNumber,
                       QgsExpressionContext,
                       QgsExpressionContextUtils,
                       QgsProject,
                       QgsVectorFileWriter,
                       QgsVectorLayer,
                       QgsFeatureRequest,
                       QgsField,
                       QgsVectorDataProvider,
                       QgsCoordinateTransform,
                       QgsCoordinateReferenceSystem,
                       QgsGeometry,
                       QgsWkbTypes,
                       QgsExpression,
                       QgsFields,
                       QgsFeature,
                       QgsProcessingParameterVectorDestination,
                       NULL)
from qgis import processing
from qgis import os
from qgis.PyQt.QtWidgets import QAction, QMessageBox, QFileDialog
from qgis.utils import iface
import math


# IMPORT COMMON SCRIPTS
import importlib.util
import sys
module_path = os.path.join(os.path.dirname(QgsProject.instance().fileName()), '_processing','common.py')
spec = importlib.util.spec_from_file_location("common", module_path)
common = importlib.util.module_from_spec(spec)
sys.modules["common"] = common
spec.loader.exec_module(common)

class MergeSelected(QgsProcessingAlgorithm):
    
    LAYERS = 'LAYERS'
    DEBUG = 'DEBUG'

    message = (
        "Merge the selected layers"
    )
    
    def name(self):
        return 'MergeSelected'
    def tr(self, string):
        return QCoreApplication.translate('Processing', string)
    def createInstance(self):
        return MergeSelected()
    def displayName(self):
        return self.tr('Merge Selected')
    def group(self):
        return self.tr('Jedsy scripts')
    def groupId(self):
        return 'jedsyscripts'
    def shortHelpString(self):
        return self.tr(self.message)

    def initAlgorithm(self, config=None):
        self.addParameter(
            QgsProcessingParameterMultipleLayers(
                name = self.LAYERS,
                description='input layers',
                defaultValue = iface.layerTreeView().selectedLayers()
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                name = self.DEBUG,
                description='Debug mode',
                optional = True,
                defaultValue = False
            )
        )
        self.addParameter(
            QgsProcessingParameterVectorDestination(
                name='OUTPUT',
                description='Merged output layer'
            )
        )
    '''
    def processAlgorithm(self, parameters, context, feedback):
    
        # CONFIG
        debug = parameters.get('DEBUG', False)
        layers_input = self.parameterAsLayerList(parameters, self.LAYERS, context)
        #projectpath = os.path.dirname(QgsProject.instance().fileName())
        #stylespath = os.path.join(projectpath, '_common')
        #root = QgsProject.instance().layerTreeRoot()
        #if debug: print(f"########## Generic tools")
        
        if debug:
            for layer in layers_input:
                print(layer)
                
        result = processing.run("native:mergevectorlayers", {
            'LAYERS': layers_input,
            'CRS':None,
            'OUTPUT':'TEMPORARY_OUTPUT'})

        layer_merged = iface.addVectorLayer(result['OUTPUT'], 'merged_layer','ogr')

        #QgsProject.instance().addMapLayer(layer_daa, False)
        #style = os.path.join(stylespath, 'daa.qml')
        #layer_daa.loadNamedStyle(style) # layer styling
        #layer_daa.triggerRepaint() # layer styling repaint
        #layers.append(layer_daa)
        

        return {} 
    '''
    def processAlgorithm(self, parameters, context, feedback):
        debug = self.parameterAsBool(parameters, 'DEBUG', context)
        layers_input = self.parameterAsLayerList(parameters, self.LAYERS, context)

        if debug:
            print("Input layers:")
            for layer in layers_input:
                print(f"  - {layer.name()}")

        output_path = self.parameterAsOutputLayer(parameters, 'OUTPUT', context)

        result = processing.run("native:mergevectorlayers", {
            'LAYERS': layers_input,
            'CRS': None,
            'OUTPUT': output_path
        }, context=context, feedback=feedback)

        return {
            'OUTPUT': result['OUTPUT']
        }
