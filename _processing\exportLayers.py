from qgis.PyQt.QtCore import QCoreApplication
from qgis.core import (QgsProcessing,
                       QgsFeatureSink,
                       QgsProcessingException,
                       QgsProcessingAlgorithm,
                       QgsProcessingParameterFeatureSource,
                       QgsProcessingParameterFeatureSink,
                       QgsProcessingParameterNumber,
                       QgsProcessingParameterString,
                       QgsProcessingParameterBoolean,
                       QgsProcessingParameterFolderDestination,
                       QgsProcessingParameterMultipleLayers,
                       QgsProcessingParameterEnum,
                       QgsProject,
                       QgsMapLayer,
                       QgsVectorFileWriter,
                       QgsCoordinateTransformContext,
                       QgsVectorLayer,
                       QgsFeatureRequest,
                       QgsField,
                       QgsVectorLayerUtils,
                       QgsExpressionContext,
                       QgsExpressionContextUtils,
                       NULL)
from qgis import processing
from qgis import os
from qgis.PyQt.QtWidgets import QAction, QMessageBox, QFileDialog
from qgis.utils import iface
import xml.etree.ElementTree as ET

# IMPORT COMMON SCRIPTS
import importlib.util
import sys
import inspect

# Get the directory of the current script
try:
    # Try to get the file path from the current frame
    current_file = inspect.getfile(inspect.currentframe())
    script_dir = os.path.dirname(current_file)
except:
    # Fallback: assume we're in the _processing directory
    script_dir = os.getcwd()

module_path = os.path.join(script_dir, 'common.py')
spec = importlib.util.spec_from_file_location("common", module_path)
common = importlib.util.module_from_spec(spec)
sys.modules["common"] = common
spec.loader.exec_module(common)


class ExportLayers(QgsProcessingAlgorithm):
    
    DEST = 'DEST'
    FORMAT = 'FORMAT'
    LAYERS = 'LAYERS'
    OVERWRITE = 'OVERWRITE'
    OPNUM = 'OPNUM'
    IGNORE = 'IGNORE'
    DEBUG = 'DEBUG'
    REMOVE = 'REMOVE'
    
    def tr(self, string):
        return QCoreApplication.translate('Processing', string)
    def createInstance(self):
        return ExportLayers()
    def name(self):
        return 'exportLayers'
    def displayName(self):
        return self.tr('Copy/Move/Convert Layers')
    def group(self):
        return self.tr('Jedsy scripts')
    def groupId(self):
        return 'jedsyscripts'
    def shortHelpString(self):
        return self.tr("CAUTIONS: target files will be overwritten")
    def flags(self):
        return QgsProcessingAlgorithm.FlagNoThreading
   
    def initAlgorithm(self, config=None):
        
        self.addParameter(
            QgsProcessingParameterMultipleLayers(
                name = self.LAYERS,
                description='input layers',
                defaultValue = iface.layerTreeView().selectedLayers()
            )
        )
        self.addParameter(
            QgsProcessingParameterEnum(
                name = self.FORMAT,
                description = 'format',
                # options = ['Geopackage'],
                options = ['Geopackage','ESRI shapefile','KML'],
                defaultValue = 'ESRI shapefile'
            )
        )
        self.addParameter(
            QgsProcessingParameterString(
                name = self.OPNUM,
                description='New Operation number',
                defaultValue = '',
                multiLine = False,
                optional = True
            )
        )
        if iface.activeLayer() == NULL:
            defaultPath = os.path.dirname(QgsProject.instance().fileName())
        else:
            defaultPath = os.path.split(iface.activeLayer().dataProvider().dataSourceUri())[0]
        self.addParameter(
            QgsProcessingParameterFolderDestination(
                name = self.DEST,
                description='destination folder',
                defaultValue = defaultPath
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.IGNORE,
                self.tr('Ignore KMZ, KML, raster'),
                defaultValue = bool(0)
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                name = self.OVERWRITE,
                description='Overwrite existing files? (CAUTION)',
                defaultValue = bool(1)
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                name = self.REMOVE,
                description='Remove original layer? (CAUTION)',
                defaultValue = bool(0)
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.DEBUG,
                self.tr('Debug mode'),
                defaultValue = bool(0)
            )
        )
        
    def processAlgorithm(self, parameters, context, feedback):
        debug = parameters['DEBUG']
        destpath = self.parameterAsString(parameters, self.DEST, context)
        formatInput = self.parameterAsInt(parameters, self.FORMAT, context)
        
        ignored_formats = ['kml','kmz']
        
        if formatInput == 0:
            driverName = 'GPKG'
            ignored_formats.append('gpkg')
        elif formatInput == 1:
            driverName = 'ESRI shapefile'
            ignored_formats.append('shp')
        elif formatInput == 2:
            driverName = 'KML'
            ignored_formats.append('kml')
        opnum = self.parameterAsString(parameters, self.OPNUM, context)
        
        if not os.path.exists(destpath): # create directory if it doesnt exist
            os.mkdir(destpath)
        print('dest path: ' + destpath)
        
        
        selectedLayers = self.parameterAsLayerList(parameters, self.LAYERS, context)
        filteredLayers = []
        # Check if 'IGNORE' is true in parameters
        if parameters['IGNORE']:
            for layer in selectedLayers:
                # Check for raster layers
                if layer.type() == QgsMapLayer.RasterLayer:
                    if debug: print(f"ignored raster: {layer}")
                    continue  # Skip raster layers
                # For vector layers, check the data provider type or file extension

                if layer.type() == QgsMapLayer.VectorLayer:
                    # if debug: print(f"vector: {layer}")
                    # Get the data source URI of the layer
                    dataSourceUri = layer.dataProvider().dataSourceUri()
                    
                    # if debug: print(dataSourceUri.lower())
                    # Extract the file path before the first '|' character
                    filePath = dataSourceUri.split('|')[0]  # Split the URI and take the first part

                    # Check if the layer is a KML or KMZ file by file extension
                    extension = filePath.split('.')[-1]
                    # if debug: print(f"extension {extension}")
                    if extension in ignored_formats:
                        if debug: print(f"ignored kml or kmz or target format: {layer}")
                        continue  # Skip KML and KMZ layers

                # If the layer is not raster, KML, or KMZ, add it to the filtered list
                filteredLayers.append(layer)
            selectedLayers = filteredLayers

        if debug:
            print('SELECTED LAYERS:')
            for layer in selectedLayers: print(layer)

        for currentLayer in selectedLayers:
            result = common.export_layer(
                currentLayer,
                opnum=opnum,
                destpath=destpath,
                debug=debug,
                overwrite=parameters['OVERWRITE']
            )
            # export_layer(input_layer, opnum='', overwrite=True, driverName="GPKG", add_layer=True, apply_style=True, destpath=None, debug=False)
            if isinstance(result, QgsVectorLayer):
                feedback.pushInfo(f"{currentLayer}\nExported to : {destpath}")
            else:
                raise QgsProcessingException(f"ERROR: {result}")
            
            # Remove source layer
            if parameters['REMOVE']:
                if debug: print(f"Removing source layer {currentLayer}")
                QgsProject.instance().removeMapLayer(currentLayer.id())
        return {}