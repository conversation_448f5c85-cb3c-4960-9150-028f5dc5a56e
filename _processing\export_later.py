from qgis.PyQt.QtCore import QCoreApplication
from qgis.core import (QgsProcessing,
                       QgsFeatureSink,
                       QgsProcessingException,
                       QgsProcessingAlgorithm,
                       QgsProcessingParameterFeatureSource,
                       QgsProcessingParameterFeatureSink,
                       QgsProcessingParameterNumber,
                       QgsProcessingParameterString,
                       QgsProcessingParameterBoolean,
                       QgsProcessingParameterFolderDestination,
                       QgsProcessingParameterMultipleLayers,
                       QgsProcessingParameterVectorLayer,
                       QgsProcessingParameterMapLayer,
                       QgsCoordinateReferenceSystem,
                       QgsProcessingFeatureSourceDefinition,
                       QgsExpressionContext,
                       QgsExpressionContextUtils,
                       QgsWkbTypes,
                       QgsProject,
                       QgsFeature,
                       QgsField,
                       QgsVectorFileWriter,
                       QgsVectorLayer,
                       QgsFeatureRequest,
                       QgsProperty,
                       QgsExpression,
                       NULL)
from qgis import processing
from qgis import os
from qgis.PyQt.QtWidgets import QAction, QMessageBox, QFileDialog
from qgis.utils import iface
from qgis.PyQt.QtCore import QVariant
import layer2kmz.layer2kmz as exporter
import time

import os
import pandas as pd
import zipfile
from openpyxl import load_workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.table import Table, TableStyleInfo
from openpyxl.styles import Font
from qgis.core import QgsVectorFileWriter


# IMPORT COMMON SCRIPTS
import importlib.util
import sys
module_path = os.path.join(os.path.dirname(QgsProject.instance().fileName()), '_common','_processing','common.py')
spec = importlib.util.spec_from_file_location("common", module_path)
common = importlib.util.module_from_spec(spec)
sys.modules["common"] = common
spec.loader.exec_module(common)


class export_later(QgsProcessingAlgorithm):

    INPUT = 'INPUT'
    DEST = 'DEST'
    BUFFER = 'BUFFER'
    KMZ = 'KMZ'
    EXCEL = 'EXCEL'
    OVERWRITE = 'OVERWRITE'
    DEBUG = 'DEBUG'
    GEOJSON = 'GEOJSON'
    
    message = (
        "Runs the script Export ALL files 2 times\n"
        "once in the EXPORT folder\n"
        "the second time in the EXPORT_ALL folder\n\n"
        
        "Use the flight path layer as input.\n\n"
        "CAUTION: it will overwite files with the same name in the target directory if Overwrite is checked\n\n"
        "ver00"
    )
    
    def tr(self, string):
        return QCoreApplication.translate('Processing', string)
    def createInstance(self):
        return export_later()
    def name(self):
        return 'export_later'
    def displayName(self):
        return self.tr('Export ALL including later')
    def group(self):
        return self.tr('Jedsy scripts')
    def groupId(self):
        return 'jedsyscripts'
    def shortHelpString(self):
        return self.tr(self.message)
    def flags(self):
        return QgsProcessingAlgorithm.FlagNoThreading
    
    def initAlgorithm(self, config=None):

        self.addParameter(
            QgsProcessingParameterVectorLayer(
                name = self.INPUT,
                description='Flight Path layer'
            )
        )
        if iface.activeLayer() == NULL:
            defaultPath = os.path.dirname(QgsProject.instance().fileName())
        else:
            defaultPath = os.path.split(iface.activeLayer().dataProvider().dataSourceUri())[0]+'/EXPORT'
        self.addParameter(
            QgsProcessingParameterFolderDestination(
                name = self.DEST,
                description='destination folder',
                defaultValue = defaultPath
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.BUFFER,
                self.tr('Buffer the flight Volumes'),
                defaultValue = bool(1)
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.KMZ,
                self.tr('Export KMZ and KML files'),
                defaultValue = bool(1)
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.EXCEL,
                self.tr('Export EXCEL files'),
                defaultValue = bool(1)
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.GEOJSON,
                self.tr('Export Mapbox files'),
                defaultValue = bool(1)
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.OVERWRITE,
                self.tr('Overwrite existing files'),
                defaultValue = bool(1)
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.DEBUG,
                self.tr('Debug mode'),
                defaultValue = bool(0)
            )
        )

        
    def processAlgorithm(self, parameters, context, feedback):
        
        # only active items
        processing.run("script:exportAll", {
            'INPUT': parameters['INPUT'],
            'BUFFER': parameters['BUFFER'],
            'KMZ': parameters['KMZ'],
            'EXCEL': parameters['EXCEL'],
            'GEOJSON': parameters['GEOJSON'],
            'OVERWRITE': parameters['OVERWRITE'],
            'LATER': False,
            'DEBUG': parameters['DEBUG'],
            'DEST': os.path.normpath(parameters['DEST'])
            })

        # all items
        processing.run("script:display_groups", {
            'BACKLOG':True,
            'HIRF':False,
            'ALLSEGMENTS':False
            })
        
        
        processing.run("script:exportAll", {
            'INPUT': parameters['INPUT'],
            'BUFFER': parameters['BUFFER'],
            'KMZ': parameters['KMZ'],
            'EXCEL': parameters['EXCEL'],
            'GEOJSON': parameters['GEOJSON'],
            'OVERWRITE': parameters['OVERWRITE'],
            'LATER': True,
            'DEBUG': parameters['DEBUG'],
            'DEST': os.path.normpath(os.path.join(os.path.dirname(parameters['DEST']), os.path.basename(parameters['DEST']) + "_ALL"))
            })
                
        # only active
        processing.run("script:display_groups", {
            'BACKLOG':False,
            'HIRF':False,
            'ALLSEGMENTS':False
            })
        
        return {}