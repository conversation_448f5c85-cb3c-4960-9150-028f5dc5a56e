from qgis.PyQt.QtCore import QCoreApplication
from PyQt5.QtCore import QSize, QEventLoop
from qgis.core import (
    QgsProcessing,
    QgsProcessingException,
    QgsProcessingAlgorithm,
    QgsProcessingParameterVectorLayer,
    QgsProcessingParameterEnum,
    QgsProcessingParameterString,
    QgsProcessingParameterFolderDestination,
    QgsProcessingParameterBoolean,
    QgsProject,
    QgsMapRendererParallelJob,
    QgsVectorLayer,
    QgsExpression,
    QgsFeatureRequest,
    QgsRectangle,
    NULL
)
from qgis import processing
import os
from qgis.utils import iface
import re
import importlib.util
import sys

module_path = os.path.join(os.path.dirname(QgsProject.instance().fileName()), '_common', '_processing', 'common.py')
spec = importlib.util.spec_from_file_location("common", module_path)
common = importlib.util.module_from_spec(spec)
sys.modules["common"] = common
spec.loader.exec_module(common)

class exportRaster(QgsProcessingAlgorithm):
    INPUT = 'INPUT'
    DEST = 'DEST'
    DEBUG = 'DEBUG'
    OVERWRITE = 'OVERWRITE'
    COUNTRY = 'COUNTRY'
    REVISION = 'REVISION'
    MAPTYPES = 'MAPTYPES'

    countries = ['Germany', 'Switzerland', 'Austria', 'Other']
    export_options = [
        "overview",
        "airRisk",
        "icao",
        "population",
        "Map (details)",
        "elevation",
        "MobileA",
        "MobileB",
        "MobileC",
        "MobileD",
        "hirf",
        "hirf_wr",
        "targets_a",
        "targets_b"
    ]

    message = (
        "Exports the following raster maps (depending on the operation size, this can take a long time):\n\n"
        "• overview\n"
        "• airRisk\n"
        "• icao\n"
        "• population (TIF needs to be converted to JPG)\n"
        "• Map (details) (TIF needs to be converted to JPG)\n"
        "• elevation\n\n"
        "• Mobile Network (Germany)\n\n"
        "• HIRF\n\n"
        "Note: Mobile network maps for countries other than Germany must still be exported manually.\n\n"
        "ver01"
    )

    def tr(self, string):
        return QCoreApplication.translate('Processing', string)

    def createInstance(self):
        return exportRaster()

    def name(self):
        return 'exportRaster'

    def displayName(self):
        return self.tr('Export Raster Maps')

    def group(self):
        return self.tr('Jedsy scripts')

    def groupId(self):
        return 'jedsyscripts'

    def shortHelpString(self):
        return self.tr(self.message)

    def flags(self):
        return QgsProcessingAlgorithm.FlagNoThreading

    def __init__(self):
        super().__init__()
        self.cancel_requested = False
        self.current_render = None

    def initAlgorithm(self, config=None):
        self.addParameter(QgsProcessingParameterVectorLayer(self.INPUT, 'Operation layer'))
        self.addParameter(QgsProcessingParameterEnum(self.COUNTRY, 'Country', options=self.countries, defaultValue=0))
        self.addParameter(QgsProcessingParameterString(self.REVISION, 'Revision number', defaultValue='00'))
        default_path = os.path.dirname(QgsProject.instance().fileName()) if iface.activeLayer() == NULL else \
            os.path.split(iface.activeLayer().dataProvider().dataSourceUri())[0] + '/RASTER'
        self.addParameter(QgsProcessingParameterFolderDestination(self.DEST, 'Destination folder', defaultValue=default_path))
        self.addParameter(QgsProcessingParameterBoolean(self.OVERWRITE, 'Overwrite', defaultValue=True))
        self.addParameter(QgsProcessingParameterBoolean(self.DEBUG, 'Debug mode', defaultValue=False))
        self.addParameter(QgsProcessingParameterEnum(self.MAPTYPES, 'Maps to export',
                                                     options=self.export_options,
                                                     defaultValue=list(range(len(self.export_options))),
                                                     allowMultiple=True))

    def cancel(self):
        self.cancel_requested = True
        if self.current_render and self.current_render.isRunning():
            self.current_render.cancel()
            print("Render cancelled by user.")

    def render_map(self, output_folder, output_filename, output_format, res_dpi, px_per_unit, revision,
                   overwrite, extent, width_extent, height_extent, debug, opnum):
        if self.cancel_requested:
            print("Cancellation requested. Skipping render.")
            return
        
        # Define raw pixel dimensions
        raw_width_px = width_extent * px_per_unit
        raw_height_px = height_extent * px_per_unit
        min_px = 2000
        max_px = 28000

        # Determine scale factor
        max_dim = max(raw_width_px, raw_height_px)

        if max_dim > max_px:
            scale = max_px / max_dim  # scale down
        elif max_dim < min_px:
            scale = min_px / max_dim   # scale up
        else:
            scale = 1.0  # no scaling

        # Apply proportional scaling
        width_px = int(raw_width_px * scale)
        height_px = int(raw_height_px * scale)
        
        
        # output filename
        filename = f"{opnum}_{output_filename}_{revision}.{output_format}"
        output_path = os.path.join(output_folder, filename)

        if not overwrite and os.path.exists(output_path):
            print(f"File exists and overwrite is False: {output_path}")
            return

        if debug:
            print(f"Rendering: {output_path}")
            print(f"Size: {width_px} x {height_px}")

        settings = iface.mapCanvas().mapSettings()
        settings.setExtent(extent)
        settings.setOutputSize(QSize(width_px, height_px))
        settings.setOutputDpi(res_dpi)

        visible_layers = QgsProject.instance().layerTreeRoot().checkedLayers()
        if not visible_layers:
            print("No visible layers found.")
            return
        
        settings.setLayers(visible_layers)
        self.current_render = QgsMapRendererParallelJob(settings)
        render = self.current_render
        loop = QEventLoop()

        def finished():
            if self.cancel_requested:
                print("Render finished after cancellation.")
            else:
                img = render.renderedImage()
                if img.save(output_path, output_format):
                    if debug:
                        print(f"Rendering complete")
                else:
                    print(f"Failed to save image")
            loop.quit()

        render.finished.connect(finished)
        render.start()
        loop.exec_()

    def processAlgorithm(self, parameters, context, feedback):
        
        opnum, layerInput = common.find_op(self.parameterAsLayer(parameters, self.INPUT, context))
        
        '''
        layerInput = self.parameterAsLayer(parameters, self.INPUT, context)
        if not re.match(r'^\d{3}_', layerInput.name()):
            raise QgsProcessingException("Input layer name must start with three digits followed by an underscore.")
        opnum = layerInput.name().split('_')[0]
        '''
        country_id = parameters[self.COUNTRY]
        debug = parameters[self.DEBUG]
        output_folder = self.parameterAsString(parameters, self.DEST, context)
        revision = self.parameterAsString(parameters, self.REVISION, context)
        overwrite = parameters[self.OVERWRITE]
        selected_maps = parameters[self.MAPTYPES]
        
        layer_clipper = common.find_layer_by_name(f"{opnum}_clipper")
        layer_airrisk = common.find_layer_by_name(f"{opnum}_AirRisk")
        layer_targets = common.find_layer_by_name(f"{opnum}_Targets")
        

        #####################################
        ############## CONFIG ###############
        #####################################

        map_types_names = [
            'Initial Assessment',
            'Overview',
            'Air Risk',
            'ICAO',
            'Population',
            'Details',
            'Elevation',
            'Mobile A',
            'Mobile B',
            'Mobile C',
            'Mobile D',
            'HIRF',
            'Targets A',
            'Targets B'
        ]        

        # filename, fmt, px_per_unit, map_type name (of export maps script), fixed zoom (optional)
        all_parameter_sets = [
            ("overview", "jpg", 0.02, 'Overview'),
            ("airRisk", "jpg", 0.02, 'Air Risk'),
            ("icao", "jpg", 0.02, 'ICAO'),
            ("population", "tif", 0.08, 'Population'),
            ("Map", "tif", 0.08, 'Details'),
            ("elevation", "jpg", 0.04, 'Elevation'),
            ("MobileA", "jpg", 0.04, 'Mobile A'),
            ("MobileB", "jpg", 0.04, 'Mobile B'),
            ("MobileC", "jpg", 0.04, 'Mobile C'),
            ("MobileD", "jpg", 0.04, 'Mobile D'),
            ("hirf", "jpg", 0.8, 'HIRF', 6000),
            ("hirf_wr", "jpg", 0.02, 'HIRF'),
            ("targets_a", "jpg", 0.8, 'Targets A', 8000),
            ("targets_b", "jpg", 0.8, 'Targets B', 1000)
        ]        


        # target ratio for maps with fixed zoom
        target_ratio = 16 / 9

        if not os.path.exists(output_folder):
            os.mkdir(output_folder)

        for i in selected_maps:
            if self.cancel_requested:
                print("Cancelling remaining renders.")
                break
            
            # Unpack with default zoom_scale
            base_params = all_parameter_sets[i]
            if debug: print(f"processing {base_params}")
            # filename, fmt, px_per_unit, map_type = base_params[:4]
            filename, fmt, px_per_unit, map_type_key = base_params[:4]
            zoom_scale = base_params[4] if len(base_params) > 4 else 5000
            
            
            # determine canvas zoom
            if filename == "airRisk" or filename == "icao" or filename == "hirf_wr":
                extent = layer_airrisk.extent()
                extent.grow(extent.width() * 0.15)  # Grow by 10% of width (applies to all sides)
            else:
                extent = layer_clipper.extent()
            width_extent = extent.width()
            height_extent = extent.height()
        
            try:
                '''
                processing.run("script:exportMaps", {
                    'INPUT': layer_clipper,
                    'MAPTYPE': map_type,
                    'COUNTRY': country_id,
                    'BACKLOG': False,
                    'DEBUG': False
                })
                '''
                processing.run("script:exportMaps", {
                    'INPUT': layer_clipper,
                    # 'MAPTYPE': exportMaps.map_types_names.index(map_type_key),
                    'MAPTYPE': map_types_names.index(map_type_key),
                    'COUNTRY': country_id,
                    'BACKLOG': False,
                    'DEBUG': debug
                })
            except Exception as e:
                feedback.pushWarning(f"Failed to prepare map '{filename}': {e}")
                print(f"Failed to prepare map '{filename}': {e}")
                continue

            # special behavior for HIRF and targets
            if filename == "hirf" or filename == "targets_a" or filename == "targets_b":

                # Set zoom
                canvas = iface.mapCanvas()
                canvas.zoomScale(zoom_scale)
                canvas.refresh()
                if debug:
                    print(f"New map scale set to: 1:{canvas.scale()}")

                expr = QgsExpression('"status" = \'Active\'')
                request = QgsFeatureRequest(expr)

                for feature in layer_targets.getFeatures(request):
                    if self.cancel_requested:
                        print("Cancelling remaining renders.")
                        break

                    feat_id = feature['id'] if 'id' in feature.fields().names() else feature.id()
                    geom = feature.geometry()
                    if not geom:
                        continue

                    # Center canvas on feature
                    canvas.setExtent(geom.boundingBox())
                    canvas.refresh()

                    # Calculate adjusted extent with predefined ratio
                    extent = canvas.extent()
                    center_x = extent.center().x()
                    center_y = extent.center().y()
                    current_width = extent.width()
                    current_height = extent.height()
                    current_ratio = current_width / current_height
                    

                    if current_ratio > target_ratio:
                        # Too wide — reduce width
                        new_height = current_height
                        new_width = new_height * target_ratio
                    else:
                        # Too tall — reduce height
                        new_width = current_width
                        new_height = new_width / target_ratio

                    half_width = new_width / 2
                    half_height = new_height / 2
                    adjusted_extent = QgsRectangle(
                        center_x - half_width, center_y - half_height,
                        center_x + half_width, center_y + half_height
                    )

                    canvas.setExtent(adjusted_extent)
                    canvas.refresh()

                    self.render_map(
                        output_folder,
                        f"{filename}_{int(feat_id):02}",
                        fmt,
                        100,
                        px_per_unit,
                        revision,
                        overwrite,
                        adjusted_extent,
                        new_width,
                        new_height,
                        debug,
                        opnum
                    )
                
            else: 
                self.render_map(output_folder, filename, fmt, 100, px_per_unit, revision, overwrite, extent, width_extent, height_extent, debug, opnum)

        return {}