from qgis.PyQt.QtCore import QCoreApplication
from qgis.core import (QgsProcessing,
                       QgsFeatureSink,
                       QgsProcessingException,
                       QgsProcessingAlgorithm,
                       QgsProcessingParameterFeatureSource,
                       QgsProcessingParameterFeatureSink,
                       QgsProcessingParameterNumber,
                       QgsProcessingParameterString,
                       QgsProcessingParameterBoolean,
                       QgsProcessingParameterFolderDestination,
                       QgsProcessingParameterMultipleLayers,
                       QgsProcessingParameterMapLayer,
                       QgsProcessingParameterEnum,
                       QgsProcessingParameterBoolean,
                       QgsCoordinateTransform,
                       QgsCoordinateReferenceSystem,
                       QgsProcessingFeatureSourceDefinition,
                       QgsProcessingContext,
                       QgsGeometry,
                       QgsPointXY,
                       QgsFeature,
                       QgsRectangle,
                       QgsVectorLayerUtils,
                       QgsProject,
                       QgsField,
                       QgsVectorFileWriter,
                       QgsVectorLayer,
                       QgsFeatureRequest,
                       QgsWkbTypes,
                       QgsLayerTreeGroup,
                       NULL)


import re
import ast
from qgis.gui import *
from qgis.utils import iface
from qgis import processing
from qgis import os
from qgis.PyQt.QtWidgets import QAction, QMessageBox, QFileDialog
from qgis.PyQt.QtCore import QVariant
import time
import tempfile

# IMPORT COMMON SCRIPTS
import importlib.util
import sys
module_path = os.path.join(os.path.dirname(QgsProject.instance().fileName()), '_common','_processing','common.py')
spec = importlib.util.spec_from_file_location("common", module_path)
common = importlib.util.module_from_spec(spec)
sys.modules["common"] = common
spec.loader.exec_module(common)

class queryOSM(QgsProcessingAlgorithm):
    
    INPUT = 'INPUT'
    DEBUG = 'DEBUG'
    QUERYLAYERS = 'QUERYLAYERS'
    DEST = 'DEST'
    CLEANBUILDINGS = 'CLEANBUILDINGS'

    message = (
        "If the FP has selected features it will only use them.\n"
        "Otherwise it will use all the features of the FP layer\n"
        "ver05"
    )
    
    def name(self):
        return 'queryOSM'
    def tr(self, string):
        return QCoreApplication.translate('Processing', string)
    def createInstance(self):
        return queryOSM()
    def displayName(self):
        return self.tr('Extract from OSM')
    def group(self):
        return self.tr('Jedsy scripts')
    def groupId(self):
        return 'jedsyscripts'
    def shortHelpString(self):
        return self.tr(self.message)
    def flags(self):
        return QgsProcessingAlgorithm.FlagNoThreading        
        
    # key, value, name, points, style points, lines, style lines, polygons, style polys
    queries = [
        ('building','','buildings',False,'',False,'',True,'buildings'),
        ('generator:method','wind_turbine','windTurbines',True,'windTurbines',False,'',False,''),
        ('power','line','powerLines',False,'',True,'power_line',False,''),
        ('amenity','school','schools',True,'education_pts',False,'',True,'education_poly'),
        ('amenity','kindergarten','kindergarten',True,'education_pts',False,'',True,'education_poly'),
        ('amenity','university','university',True,'education_pts',False,'',True,'education_poly'),
        ('amenity','college','college',True,'education_pts',False,'',True,'education_poly'),
        ('leisure','pitch','pitch',False,'',False,'',True,'stadium_poly'),
        ('leisure','sports_centre','sports_centre',True,'sports_centre_pts',False,'',True,'sports_centre_poly'),
        ('leisure','stadium','stadium',False,'',False,'',True,'stadium_poly'),
        ('landuse','military','military',False,'',False,'',True,'military_poly'),
        ('healthcare','hospital','hospital',False,'',False,'',True,'hospital_poly'),
        ('highway','motorway','highway',False,'',True,'highway_line',False,''),
        ('railway','rail','railway',False,'',True,'railway_line',False,''),
        ('aeroway','helipad','helipads',False,'',False,'',True,'helipads'),
        ('sport','model_aerodrome','Model Aerodome',False,'',False,'',True,'modelAerodome_poly')
        ]


        
    optionsList = [item[0] + ' ' +item[1] for item in queries]
 
    def initAlgorithm(self, config=None):
        
        '''
        if iface.activeLayer() == NULL:
            defaultPath = os.path.dirname(QgsProject.instance().fileName())
        else:
            defaultPath = os.path.split(iface.activeLayer().dataProvider().dataSourceUri())[0]+'/OSM'
        '''
        if iface.activeLayer() == NULL:
            defaultPath = os.path.dirname(QgsProject.instance().fileName())
        else:
            opnum, layer_fp = common.find_op(iface.activeLayer())
            defaultPath = os.path.split(layer_fp.dataProvider().dataSourceUri())[0]+'/EXPORT'        
        
        self.addParameter(
            QgsProcessingParameterMapLayer(
                name = self.INPUT,
                description='input layer'
            )
        )
        self.addParameter(
            QgsProcessingParameterEnum(
                name = self.QUERYLAYERS,
                description='Layers to be interrogated',
                options = self.optionsList,
                allowMultiple = True,
                # defaultValue = [0,1,2]
                defaultValue = list(range(len(self.optionsList)))
            )
        )

        self.addParameter(
            QgsProcessingParameterString(
                name='SUFFIX',
                description='Optional suffix to add to output names',
                defaultValue='',
                optional=True
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                name = self.CLEANBUILDINGS,
                description='Remove small buildings',
                optional = True,
                defaultValue = True
            )
        )
        
        self.addParameter(
            QgsProcessingParameterBoolean(
                name = self.DEBUG,
                description='Debug mode',
                optional = True,
                defaultValue = False
            )
        )
        self.addParameter(
            QgsProcessingParameterFolderDestination(
                name = self.DEST,
                description='Destination folder',
                defaultValue = defaultPath
            )
        )


    def processAlgorithm(self, parameters, context, feedback):
        
        def saveAsEditable(layer, filename):
            options = QgsVectorFileWriter.SaveVectorOptions()
            options.driverName = "GPKG"
            options.layerName = layer.name()
            _ = QgsVectorFileWriter.writeAsVectorFormatV3(layer, filename, context.transformContext(), options)
            return filename
    
        def fieldCheck(layer, field_name):
            # Find the existing field name in a case-insensitive way
            existing_field_names = [f.name() for f in layer.fields()]
            matched_field_name = next((f for f in existing_field_names if f.lower() == field_name.lower()), None)

            if matched_field_name:
                index = layer.fields().indexFromName(matched_field_name)
                if debug: print(f"field: {matched_field_name} id {index}")
                return index
            else:
                # Field not found, add it
                field_type = QVariant.Int if field_name.lower() == 'id' else QVariant.String
                new_field = QgsField(field_name, field_type)

                layer.startEditing()
                if layer.dataProvider().addAttributes([new_field]):
                    layer.updateFields()
                    if debug: print(f"Field '{field_name}' added successfully.")
                else:
                    if debug: print(f"Failed to add field '{field_name}'.")
                layer.commitChanges()

                index = layer.fields().indexFromName(field_name)
                if debug: print(f"field: {field_name} id {index}")
                return index                

        def getFieldValueCaseInsensitive(feature, field_name):
            for key in feature.fields().names():
                if key.lower() == field_name.lower():
                    return feature[key]
            return None
        
        def elaborateLayer(layer,styleName, debug = False, clean_buildings = True):
            if debug: print(f"Elaborate layer: {layer.name()}, {layer}")
            layer.setName(layerName)
            styleFileName = styleName + '.qml'
            style = os.path.join(stylespath, styleFileName)
            layer.loadNamedStyle(style)
            layer.triggerRepaint()

            # remove low or empty-level buildings
            if styleName == "buildings" and clean_buildings:
                if debug: print(f"cleaning small buildings")
                layer.startEditing()

                # case-insensitive field match
                levels_field = next((f.name() for f in layer.fields() if f.name().lower() in ['building:levels', 'building_levels']), None)
                if levels_field:
                    levels_idx = layer.fields().indexFromName(levels_field)

                    ids_to_delete = []
                    for f in layer.getFeatures():
                        val = f[levels_idx]
                        if val is None or str(val).strip() == '':
                            ids_to_delete.append(f.id())
                            continue
                        try:
                            if int(val) < 10:
                                ids_to_delete.append(f.id())
                        except (ValueError, TypeError):
                            ids_to_delete.append(f.id())  # Treat uncastable values as invalid too

                    if ids_to_delete:
                        if debug: print(f"deleting {len(ids_to_delete)} low or empty features ")
                        layer.deleteFeatures(ids_to_delete)

                layer.commitChanges()


            # IMPORT POINTS
            if debug: print('IMPORT POINTS')
            autoImportItem = next((item for item in autoImport if item[1] == query[2]), None) # find the current layer name in the autoImport list
            if debug: print(f"autoImportItem: {autoImportItem}")
            if autoImportItem:

                # Load the existing point layer (Air risk or Ground Risk)
                layersSrc = QgsProject.instance().mapLayersByName(opnum + '_' + autoImportItem[0])
                layerTarget = layersSrc[0] if layersSrc else None
                if debug: print(f"layerTarget: {layerTarget}")
                
                processing.run("script:genericTools", {
                    'LAYERS': layerTarget,
                    'GEOM':True,
                    'ID':True,
                    'STYLE':True
                    }
                )
                
                if not layerTarget:
                    if debug: print('layerTarget not found, skipping autoimport')
                    return
                layerTarget_pr = layerTarget.dataProvider()

                descr_index = fieldCheck(layerTarget,'descr')
                name_index = fieldCheck(layerTarget,'name')
                notes_index = fieldCheck(layerTarget,'notes')
                

                
                crs_src = layer.crs() # Source CRS
                crs_dest = layerTarget.crs() # Destination CRS
                transform = QgsCoordinateTransform(crs_src, crs_dest, QgsProject.instance())


                # Iterate over each feature of the source layer
                if debug: print(f"Iterate over each feature of the source layer: {layer}")
                layerTarget.startEditing()
                for feature in layer.getFeatures():
                    point_feature = NULL
                    if debug: print(f"Feature name: {feature}")
                    if layer.geometryType() == QgsWkbTypes.PointGeometry:
                        if debug: print(f"Get and transform point")
                        pointGeo = feature.geometry()
                        pointGeo.transform(transform) # crs transform
                        # Create a new feature for the point layer
                        if debug: print(f"Create a new feature for the point layer")
                        point_feature = QgsFeature(layerTarget.fields())
                        point_feature.setGeometry(QgsGeometry.fromPointXY(QgsPointXY(pointGeo.asPoint())))
                    else:
                        if debug: print(f"Get and transform centroid of polygon")
                        centroid = feature.geometry().centroid()
                        centroid.transform(transform) # crs transform
                        # Create a new feature for the point layer
                        point_feature = QgsFeature(layerTarget.fields())
                        point_feature.setGeometry(QgsGeometry.fromPointXY(QgsPointXY(centroid.asPoint())))
                    
                    # Check if a point with the same coordinates already exists
                    if debug: print(f"Check if a point with the same coordinates already exists")
                    point_exists = False
                    for featureTarget in layerTarget.getFeatures():
                        if featureTarget.geometry().asPoint() == point_feature.geometry().asPoint():
                            point_exists = True
                            break
                    if point_exists:
                        feedback.pushInfo(f"Skipping point as it already exists")
                        continue

                    # ATTRIBUTES
                    if debug: print(f"ATTRIBUTES")
                    point_feature.setAttribute(descr_index, autoImportItem[2])
                    # point_feature.setAttribute(name_index, feature['name'])
                    point_feature.setAttribute(name_index, getFieldValueCaseInsensitive(feature, 'name'))
                    point_feature.setAttribute(notes_index, feature['other_tags'])
                    layerTarget_pr.addFeature(point_feature)

                layerTarget.commitChanges()

        
        #############################
        ########## CONFIG ###########
        #############################
        #layer_input = self.parameterAsLayer(parameters, self.INPUT, context)
        opnum, layer_input = common.find_op(self.parameterAsLayer(parameters, self.INPUT, context))
        
        debug = self.parameterAsBoolean(parameters, self.DEBUG, context)
        clean_buildings = self.parameterAsBoolean(parameters, self.CLEANBUILDINGS, context)
        # opnum = layer_input.name().split('_')[0]
        queries = [self.queries[i] for i in parameters['QUERYLAYERS'] if i < len(self.queries)]
        destpath = self.parameterAsString(parameters, self.DEST, context)
        if destpath == '/OSM' or destpath.replace("/", "\\").startswith(tempfile.gettempdir()):
            raise QgsProcessingException("ERROR: The destination path is invalid.\nPlease report this error to Filippo")
        suffix = self.parameterAsString(parameters, 'SUFFIX', context)
        suffix = '_' + suffix if suffix else ''
        
        # layers to be auto imported (target layer, source layer, descr)
        autoImport = [
            ('AirRisk','helipads','Heliport'),
            ('AirRisk','Model Aerodome','Model'),
            ('GroundRisk','windTurbines','Wind turbine')
        ]
        
        projectpath = os.path.dirname(QgsProject.instance().fileName())
        stylespath = os.path.join(projectpath, '_common')
        layerDir = os.path.dirname(layer_input.source())
        if not os.path.exists(destpath): # create directory if it doesnt exist
            os.mkdir(destpath)

        buffer = 1000 # meters
        
        
        # create group for new layers
        root = QgsProject.instance().layerTreeRoot()
        layer_node = root.findLayer(layer_input.id())
        parent_group = layer_node.parent() if layer_node else root

        group_name = "OSM" + suffix

        # check if group already exists
        existing_group = next(
            (child for child in parent_group.children() 
             if isinstance(child, QgsLayerTreeGroup) and child.name() == group_name),
            None
        )

        target_group = existing_group if existing_group else parent_group.insertGroup(0, group_name)
        


        # BUFFER INPUT LAYER
        if debug: print(f"# BUFFER INPUT LAYER")
        selected_features = layer_input.selectedFeatures()
        if debug: print(f"selected_features {selected_features}")
        if selected_features: # if there are selected features, buffer only them
            result = processing.run("native:buffer", {
                'INPUT': QgsProcessingFeatureSourceDefinition(
                    layer_input.source(),
                    selectedFeaturesOnly=True
                ),
                'DISTANCE': buffer,
                'DISSOLVE': True,
                'OUTPUT': 'TEMPORARY_OUTPUT'
            })
        else: # otherwise use the entire layer
            result = processing.run("native:buffer", {
                'INPUT':layer_input,
                'DISTANCE':buffer,
                'DISSOLVE':True,
                'OUTPUT':'TEMPORARY_OUTPUT'
                })

        layerBuffered = QgsProject.instance().addMapLayer(result['OUTPUT'])
        #layer = layerBuffered



        # BOUNDING BOX
        crsSrc = layerBuffered.crs()
        crsDest = QgsCoordinateReferenceSystem(3857)
        
        if crsSrc is not crsDest:
            xform = QgsCoordinateTransform(crsSrc, crsDest, QgsProject.instance())
            bbox = layerBuffered.extent()
            xmin, ymin = xform.transform(bbox.xMinimum(), bbox.yMinimum())
            xmax, ymax = xform.transform(bbox.xMaximum(), bbox.yMaximum())
            ext = QgsRectangle(xmin, ymin, xmax, ymax)
        else:
            ext = layerBuffered.extent()
        bbox = "{:.9f},{:.9f},{:.9f},{:.9f}".format(ext.xMinimum(),ext.xMaximum(),ext.yMinimum(),ext.yMaximum())

        total = 100.0 / len(queries)
        
        
        # QUERIES
        if debug: print("\nQUERIES")
        for counter, query in enumerate(queries):
            if feedback.isCanceled():
                break
            feedback.setProgress(int(counter * total)) 
            
            try:
                layerName = opnum + '_' + query[2] + suffix
                if debug: print(f"# {counter} {layerName}")
                
                # Collect all layers with the specified name
                layers_to_delete = [layer.id() for layer in QgsProject.instance().mapLayers().values() if layer.name() == layerName]
                for layer_id in layers_to_delete:
                    if debug: print(f"deleting layer id {layer_id}")
                    QgsProject.instance().removeMapLayer(layer_id)
                
                fileName = os.path.join(destpath,layerName + '.gpkg')
                
                alg_params = {
                    'EXTENT': bbox,
                    'KEY': query[0],
                    'VALUE': query[1],
                    'SERVER': 'https://lz4.overpass-api.de/api/interpreter',
                    'TIMEOUT': 25,
                    'CUSTOM': False,
                    'TAGS': '',
                    'OUTPUT': 'TEMPORARY_OUTPUT'
                }
                request = processing.run('quickosm:buildqueryextent', alg_params)
            
                file = processing.run("native:filedownloader", {'URL':request['OUTPUT_URL'], 'OUTPUT':fileName})
                data = processing.run('quickosm:openosmfile', {"FILE":file["OUTPUT"]})
                
                time.sleep(1)
                
                if query[3]:  # Points
                    saved = os.path.join(destpath, layerName + "_points.gpkg")
                    saveAsEditable(data['OUTPUT_POINTS'], saved)
                    layer_output = QgsVectorLayer(saved, layerName, 'ogr')
                    QgsProject.instance().addMapLayer(layer_output, False)
                    target_group.insertLayer(0, layer_output)
                    elaborateLayer(layer_output, query[4], debug, clean_buildings)

                if query[5]:  # Lines
                    saved = os.path.join(destpath, layerName + "_lines.gpkg")
                    saveAsEditable(data['OUTPUT_LINES'], saved)
                    layer_output = QgsVectorLayer(saved, layerName, 'ogr')
                    QgsProject.instance().addMapLayer(layer_output, False)
                    target_group.insertLayer(0, layer_output)
                    elaborateLayer(layer_output, query[6], debug, clean_buildings)

                if query[7]:  # Polygons
                    saved = os.path.join(destpath, layerName + "_poly.gpkg")
                    saveAsEditable(data['OUTPUT_MULTIPOLYGONS'], saved)
                    layer_output = QgsVectorLayer(saved, layerName, 'ogr')
                    QgsProject.instance().addMapLayer(layer_output, False)
                    target_group.insertLayer(0, layer_output)
                    elaborateLayer(layer_output, query[8], debug, clean_buildings)


            except Exception as e:
                raise QgsProcessingException("ERROR:", e)

        QgsProject.instance().removeMapLayer(layerBuffered.id())

        return {}