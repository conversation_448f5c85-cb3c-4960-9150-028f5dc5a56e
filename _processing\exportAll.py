from qgis.PyQt.QtCore import QCoreApplication
from qgis.core import (QgsProcessing,
                       QgsFeatureSink,
                       QgsProcessingException,
                       QgsProcessingAlgorithm,
                       QgsProcessingParameterFeatureSource,
                       QgsProcessingParameterFeatureSink,
                       QgsProcessingParameterNumber,
                       QgsProcessingParameterString,
                       QgsProcessingParameterBoolean,
                       QgsProcessingParameterFolderDestination,
                       QgsProcessingParameterMultipleLayers,
                       QgsProcessingParameterVectorLayer,
                       QgsProcessingParameterMapLayer,
                       QgsCoordinateReferenceSystem,
                       QgsProcessingFeatureSourceDefinition,
                       QgsExpressionContext,
                       QgsExpressionContextUtils,
                       QgsWkbTypes,
                       QgsProject,
                       QgsFeature,
                       QgsField,
                       QgsVectorFileWriter,
                       QgsVectorLayer,
                       QgsFeatureRequest,
                       QgsProperty,
                       QgsExpression,
                       QgsVectorDataProvider,
                       QgsFields,
                       QgsProcessingOutputLayerDefinition,
                       NULL)

from qgis import processing
from qgis.PyQt.QtWidgets import QAction, QMessageBox, QFileDialog
from qgis.utils import iface
from qgis.PyQt.QtCore import QVariant
import layer2kmz.layer2kmz as exporter
import time
import os
import gc
import pandas as pd
import zipfile
from openpyxl import load_workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.worksheet.table import Table, TableStyleInfo
from openpyxl.styles import Font
import errno
import glob
from openpyxl.workbook.properties import CalcProperties

# IMPORT COMMON SCRIPTS
import importlib.util
import sys
import inspect

# Get the directory of the current script
try:
    # Try to get the file path from the current frame
    current_file = inspect.getfile(inspect.currentframe())
    script_dir = os.path.dirname(current_file)
except:
    # Fallback: assume we're in the _processing directory
    script_dir = os.getcwd()

module_path = os.path.join(script_dir, 'common.py')
spec = importlib.util.spec_from_file_location("common", module_path)
common = importlib.util.module_from_spec(spec)
sys.modules["common"] = common
spec.loader.exec_module(common)

# Ensure xlsxwriter is available
def ensure_xlsxwriter():
    import importlib.util
    import subprocess
    import os
    if importlib.util.find_spec("xlsxwriter") is None:
        python_exe = os.path.join(os.environ.get('OSGEO4W_ROOT', ''), 'bin', 'python.exe')
        if not os.path.exists(python_exe):
            raise RuntimeError(f"Cannot find Python executable at: {python_exe}")
        subprocess.run([python_exe, "-m", "pip", "install", "xlsxwriter"], check=True)
ensure_xlsxwriter()


class exportAll(QgsProcessingAlgorithm):

    INPUT = 'INPUT'
    DEST = 'DEST'
    BUFFER = 'BUFFER'
    KMZ = 'KMZ'
    EXCEL = 'EXCEL'
    OVERWRITE = 'OVERWRITE'
    DEBUG = 'DEBUG'
    GEOJSON = 'GEOJSON'
    LATER = 'LATER'
    CLEANUP = 'CLEANUP'
    
    message = (
        "Script used to export all KMZ, KML, GEOJSON and Excel files, already styled and usable in googlemaps.\n"
        "Use the flight path layer as input.\n\n"
        "CAUTION: it will overwite files with the same name in the target directory if Overwrite is checked\n\n"
        "ver26"
    )
    
    def tr(self, string):
        return QCoreApplication.translate('Processing', string)
    def createInstance(self):
        return exportAll()
    def name(self):
        return 'exportAll'
    def displayName(self):
        return self.tr('Export ALL files')
    def group(self):
        return self.tr('Jedsy scripts')
    def groupId(self):
        return 'jedsyscripts'
    def shortHelpString(self):
        return self.tr(self.message)
    def flags(self):
        return QgsProcessingAlgorithm.FlagNoThreading
    
    def initAlgorithm(self, config=None):

        self.addParameter(
            QgsProcessingParameterVectorLayer(
                name = self.INPUT,
                description='Selected layer'
            )
        )
        
        if iface.activeLayer() == NULL:
            defaultPath = os.path.dirname(QgsProject.instance().fileName())
        else:
            opnum, layer_fp = common.find_op(iface.activeLayer())
            defaultPath = os.path.split(layer_fp.dataProvider().dataSourceUri())[0]+'/EXPORT'
            # defaultPath = os.path.split(iface.activeLayer().dataProvider().dataSourceUri())[0]+'/EXPORT'
            
        self.addParameter(
            QgsProcessingParameterFolderDestination(
                name = self.DEST,
                description='destination folder',
                defaultValue = defaultPath
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.BUFFER,
                self.tr('Buffer the flight Volumes'),
                defaultValue = True
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.KMZ,
                self.tr('Export KMZ and KML files'),
                defaultValue = True
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.EXCEL,
                self.tr('Export EXCEL files'),
                defaultValue = True
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.GEOJSON,
                self.tr('Export Mapbox GEOJSON files'),
                defaultValue = True,
                optional = True
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.OVERWRITE,
                self.tr('Overwrite existing files'),
                defaultValue = True
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.LATER,
                self.tr('Export items marked for Later'),
                defaultValue = False
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.DEBUG,
                self.tr('Debug mode'),
                defaultValue = False
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.CLEANUP,
                self.tr('Clean temporary files'),
                optional = True,
                defaultValue = True
            )
        )

        
    def processAlgorithm(self, parameters, context, feedback):
        
        def checkOverwrite(output):
            #if parameters['OVERWRITE'] is False and os.path.exists(output):
            if self.parameterAsBool(parameters, self.OVERWRITE, context) is False and os.path.exists(output):
                raise QgsProcessingException(f"ERROR: the following file already exists and Overwriting is disabled: {output}")
        
        def findField(layer, name): # Find field by name (case insensitive)
            for field in layer.fields():
                if field.name().lower() == name.lower():
                    return layer.fields().indexFromName(field.name())
            return -1  # Returns -1 if the field is not found


        def bufferLayer(layer, volumes, destpathKml, debug=False):
            if debug:
                print(f"\nbufferLayer")
                print(f"Buffering {layer.name()}")

            for volume in volumes:
                volumeName = volume.removesuffix('Mod')
                layerTypeName = layer.name().split('_')[1]

                # ignore "low" for flight path
                if layerTypeName == "fp" and volumeName == "low":
                    continue

                outputname = layer.name() + '_' + volumeName
                output = destpathKml + '/' + outputname + '.kml'
                checkOverwrite(output)

                if debug:
                    print(f"Volume {volumeName} - layerTypeName {layerTypeName}")
                    print(f"output {output}")

                # prepare selection expression (to exclude hidden features)
                if later:
                    expression = '"status" != \'Hidden\''
                else:
                    expression = '"status" != \'Hidden\' AND "status" != \'Later\''
                
                # special treatment for low areas
                if volumeName == "low":
                    distance = 0
                    expression = expression + ' AND "fmode" = \'Low\''
                else:
                    distance = QgsProperty.fromExpression(volume)
                

                layer.removeSelection()  # Clear any previous selection
                layer.selectByExpression(expression)  

                if layer.selectedFeatureCount() > 0:
                    # Buffer only selected features
                    result = processing.run("native:buffer", {
                        'INPUT': QgsProcessingFeatureSourceDefinition(layer.id(), selectedFeaturesOnly=True),
                        'DISTANCE': distance,
                        'OUTPUT': 'memory:',  # Output to memory to manipulate before saving
                        'DISSOLVE': True
                    })

                    # Get buffered layer from memory
                    buffered_layer = result['OUTPUT']
                    
                    '''
                    # Add the 'type' field to store layerTypeName
                    dp = buffered_layer.dataProvider()
                    dp.addAttributes([QgsField('type', QVariant.String)])
                    buffered_layer.updateFields()

                    # Populate the 'type' field with volumeName
                    buffered_layer.startEditing()
                    for feat in buffered_layer.getFeatures():
                        feat['type'] = volumeName
                        buffered_layer.updateFeature(feat)
                    buffered_layer.commitChanges()
                    '''

                    # Add the 'type' field if missing
                    dp = buffered_layer.dataProvider()
                    existing_fields = [f.name().lower() for f in buffered_layer.fields()]
                    if 'type' not in existing_fields:
                        if debug: print(f"Adding 'type' field to buffered_layer")
                        dp.addAttributes([QgsField('type', QVariant.String)])
                        buffered_layer.updateFields()

                    # Populate the 'type' field with volumeName
                    buffered_layer.startEditing()
                    idx_type = findField(buffered_layer, 'type')
                    if idx_type == -1:
                        raise QgsProcessingException(f"[ERROR] 'type' field not found in buffered_layer: {buffered_layer.name()}")

                    count_updated = 0
                    for feat in buffered_layer.getFeatures():
                        success = buffered_layer.changeAttributeValue(feat.id(), idx_type, volumeName)
                        if not success:
                            print(f"[WARNING] Failed to update feature {feat.id()} in layer {buffered_layer.name()}")
                        else:
                            count_updated += 1

                    buffered_layer.commitChanges()
                    if debug: print(f"Updated {count_updated} features in {buffered_layer.name()} with type = {volumeName}")
                    
                    
                    # Save the modified layer to KML
                    QgsVectorFileWriter.writeAsVectorFormat(buffered_layer, output, "utf-8", driverName="KML")
                    
                    path = os.path.normpath(output)
                    
                    # append to layers lists
                    if volumeName == "low":
                        lowLayers.append(path)  
                        if debug: print(f"Added to lowLayers: {path}")
                    else:
                        bufferedLayers.append(path)
                        if debug: print(f"Added to bufferedLayers: {path}")
                        
                    
                else:
                    if debug:
                        print(f"No feature selected with expression {expression}; skipping buffering of {layer}.")


            # Clear the selection after processing
            layer.removeSelection()


        def mergeVolumes():
            if debug: print(f"\nMERGE VOLUMES")
            if self.parameterAsBool(parameters, self.LATER, context):
                outputname = opnum + '_Volumes_All'
            else:
                outputname = opnum + '_Volumes'

            # final file to save
            output = os.path.join(destpathKml, f"{outputname}.kml")
            checkOverwrite(output)

            if self.parameterAsBool(parameters, self.OVERWRITE, context):
                for layer in QgsProject.instance().mapLayersByName(outputname):
                    QgsProject.instance().removeMapLayer(layer.id())
           
            if debug: print(f"Native Merge tool")
            result = processing.run("native:mergevectorlayers",
                {
                'LAYERS': bufferedLayers,
                'OUTPUT':'TEMPORARY_OUTPUT'
                })
            layerMerged = QgsProject.instance().addMapLayer(result['OUTPUT'])
            style = os.path.join(stylespath, 'Volumes.qml')
            layerMerged.loadNamedStyle(style) # layer styling
            layerMerged.triggerRepaint() # layer styling repaint

            '''
            # export to KML
            if debug: print(f"Dissolve tool")
            processing.run("native:dissolve",
                {
                'INPUT':layerMerged,
                'FIELD':['type'],
                'SEPARATE_DISJOINT':False,
                'OUTPUT': output
                })
            QgsProject.instance().removeMapLayer(layerMerged.id())
            layerNew = QgsVectorLayer(output, outputname, 'ogr')
            if not layerNew.isValid():
                print(f"Failed to load layer: {output}")
                feedback.pushInfo(f"Failed to load layer: {output}")
            QgsProject.instance().addMapLayer(layerNew, True)
            '''
            # Dissolve
            if debug: print(f"Native Dissolve tool")
            layer_dissolved = processing.run("native:dissolve", {
                'INPUT': layerMerged,
                'FIELD': ['type'],
                'SEPARATE_DISJOINT': False,
                'OUTPUT': 'TEMPORARY_OUTPUT'
            })['OUTPUT']
            QgsProject.instance().removeMapLayer(layerMerged.id())
            
            # Refactor fields to force unique FIDs
            if debug: print(f"Native refactorfields tool")
            cleaned = processing.run("native:refactorfields", {
                'INPUT': layer_dissolved,
                'FIELDS_MAPPING': [
                    {'name': 'Name',         'expression': '"Name"',         'type': 10, 'type_name': 'text',     'length': 0, 'precision': 0, 'sub_type': 0, 'alias': '', 'comment': ''},
                    {'name': 'folder',       'expression': '"folder"',       'type': 10, 'type_name': 'text',     'length': 0, 'precision': 0, 'sub_type': 0, 'alias': '', 'comment': ''},
                    {'name': 'type',         'expression': '"type"',         'type': 10, 'type_name': 'text',     'length': 0, 'precision': 0, 'sub_type': 0, 'alias': '', 'comment': ''}
                ],
                'OUTPUT': output
            })['OUTPUT']
            
            layerNew = QgsVectorLayer(cleaned, outputname, 'ogr')
            if not layerNew.isValid():
                print(f"Failed to load layer: {output}")
                feedback.pushInfo(f"Failed to load layer: {output}")
            QgsProject.instance().addMapLayer(layerNew, True)

            
            
            layerNew.loadNamedStyle(style) # layer styling
            layerNew.triggerRepaint() # layer styling repaint
            layers.append(layerNew)
            if debug: print(f"Dissolved layer layerNew {layerNew.name()}")
            return layerNew

        def mergeLow():
            if debug: print(f"\nMERGE LOW LAYERS")
            
            #if parameters['LATER']:
            if self.parameterAsBool(parameters, self.LATER, context):
                outputname = opnum + '_Low_All'
            else:
                outputname = opnum + '_Low'
            
            output = destpathKml + '/' + outputname + '.kml'
            checkOverwrite(output)
            #if parameters['OVERWRITE']:
            if self.parameterAsBool(parameters, self.OVERWRITE, context):
                for layer in QgsProject.instance().mapLayersByName(outputname):
                    QgsProject.instance().removeMapLayer(layer.id())
                if os.path.exists(output):
                    os.remove(output)                    
           
            if debug: print(f"Native Merge tool")
            result = processing.run("native:mergevectorlayers",
                {
                'LAYERS': lowLayers,
                'OUTPUT':'TEMPORARY_OUTPUT'
                })
            layerMerged = QgsProject.instance().addMapLayer(result['OUTPUT'])
            style = os.path.join(stylespath, 'Low.qml')
            layerMerged.loadNamedStyle(style) # layer styling
            layerMerged.triggerRepaint() # layer styling repaint
            
            

            if debug: print(f"Dissolve tool")
            processing.run("native:dissolve",
                {
                'INPUT':layerMerged,
                'FIELD':['type'],
                'SEPARATE_DISJOINT':False,
                'OUTPUT': output
                })
            
            QgsProject.instance().removeMapLayer(layerMerged.id())
            #layerNew = iface.addVectorLayer(output, outputname,'ogr')
            #QgsProject.instance().addMapLayer(layerNew, False)
            layerNew = QgsVectorLayer(output, outputname, 'ogr')
            if not layerNew.isValid():
                print(f"Failed to load layer: {output}")
                feedback.pushInfo(f"Failed to load layer: {output}")
            QgsProject.instance().addMapLayer(layerNew, True)
            
            layerNew.loadNamedStyle(style) # layer styling
            layerNew.triggerRepaint() # layer styling repaint
            #layers.append(layerNew)
            if debug: print(f"Dissolved layer layerNew {layerNew.name()}")
            return layerNew


        def exportSections(layer):
            # Check if layer contains more than one feature
            if layer.featureCount() > 1:
                if debug:
                    print(f"Exporting individual sections of {layer.name()}")

                # Extract layer type from layer name (assumes name format includes type after an underscore)
                layerTyp = layer.name().split('_')[1]

                # Iterate through each feature in the layer
                if later:
                    exclude_statuses = ['Later']
                else:
                    exclude_statuses = ['Hidden', 'Later']
                
                for feature in layer.getFeatures():
                    # Check feature "status" to determine if it should be processed
                    if feature['status'] not in exclude_statuses:
                        # Create a temporary layer for the current feature based on its ID
                        srcId = str(feature['id'])
                        searchStr = '"id" = \'' + srcId + '\''
                        layer.selectByExpression(searchStr, QgsVectorLayer.SetSelection)
                        layerTemp = layer.materialize(QgsFeatureRequest().setFilterFids(layer.selectedFeatureIds()))

                        bufferedLayers = []
                        # Process each volume in volumes for the current feature
                        for volume in volumes:
                            voulmeName = volume.removesuffix('Mod')
                            if debug:
                                print(f"Feature: {feature['id']} volume: {volume}")
                            
                            # Construct output file path for the buffered output
                            outputname = opnum + '_map_' + layerTyp + '_' + str(feature['id']) + '_' + voulmeName
                            output = destpathKml + '/' + outputname + '.kml'
                            checkOverwrite(output)
                            
                            # Run the buffer processing algorithm on the temporary layer
                            result = processing.run("native:buffer",
                                {
                                'INPUT': layerTemp,
                                'DISTANCE': QgsProperty.fromExpression(volume),
                                'OUTPUT': output,
                                'DISSOLVE': True
                                })
                            bufferedLayers.append(result['OUTPUT'])
                        
                        # After processing all volumes, merge the buffered layers
                        if debug:
                            print('Merging volumes')
                        outputname = opnum + '_map_' + f"{int(feature['id']):02d}"
                        output = destpathKml + '/' + outputname + '.kml'
                        checkOverwrite(output)
                        merged = processing.run("native:mergevectorlayers",
                            {
                            'LAYERS': bufferedLayers,
                            'OUTPUT': output
                            })

                # Clear selection after processing all features
                layer.removeSelection()


        def reproject(layer, targetCrs, styleLayer):
            dirty = False
            if layer.crs() != targetCrs:
                # new_name = layer.name() + "_reproj"
                new_name = layer.name()
                if debug: print(f"Reprojecting: {layer.name()} - targetCrs {targetCrs} - styleLayer {styleLayer} - new_name {new_name}")
                parameter = {
                    'INPUT': layer,
                    'TARGET_CRS': targetCrs,
                    #'OUTPUT': 'memory:'# + new_name
                    'OUTPUT':'TEMPORARY_OUTPUT'
                }

                result = processing.run('native:reprojectlayer', parameter)['OUTPUT']
                dirty = True
                layer = QgsProject.instance().addMapLayer(result)
                layer.setName(new_name)
                
                if styleLayer:
                    if debug: print(f"Styling")
                    style = os.path.join(projectpath, '_common', layer.name().split('_')[1] + '.qml')
                    layer.loadNamedStyle(style)
                    layer.triggerRepaint()
            if debug: print(f"Reproject function result:  {layer} - crs {layer.crs()} - dirty {dirty}")
            return layer, dirty

        def sanitize_notes(value):
            if value:
                # Replace problematic characters or escape them
                sanitized_value = value.replace('"', "")  # Example: Replace double quotes with single quotes
                return sanitized_value[:1024]  # Truncate to avoid exceeding length limit
            return value
        
        debug = parameters.get('DEBUG', False)
        if debug: print(f"########## exportAll processing tool")

        def is_file_writable(filepath):
            try:
                if os.path.exists(filepath):
                    with open(filepath, 'a'):
                        pass  # try opening in append mode
                else:
                    # Try creating then removing the file
                    with open(filepath, 'w') as f:
                        f.write('')
                    os.remove(filepath)
                return True
            except Exception as e:
                return False




        
        
        #####################################
        ############## CONFIG ###############
        #####################################

        opnum, layer_input = common.find_op(self.parameterAsLayer(parameters, self.INPUT, context))
        destpath = self.parameterAsString(parameters, self.DEST, context)
        later = parameters.get('LATER', False)
        layer_low = QgsVectorLayer()
        projectpath = os.path.dirname(QgsProject.instance().fileName())
        stylespath = os.path.join(projectpath, '_common')
        root = QgsProject.instance().layerTreeRoot()
        target_crs = QgsCoordinateReferenceSystem('EPSG:4326')
        
        # input layers
        # name, geotype, required, accept multiple, buffer, export kmz
        layerTypes = [
            ('fp',QgsWkbTypes.LineGeometry,True,True,True,False),
            ('Targets',QgsWkbTypes.PointGeometry,True,False,False,True),
            ('AirRisk',QgsWkbTypes.PointGeometry,True,False,False,True),
            ('AirRiskAreas',QgsWkbTypes.PolygonGeometry,False,False,False,False),
            ('GroundRisk',QgsWkbTypes.PointGeometry,True,False,False,True),
            ('FlightArea',QgsWkbTypes.PolygonGeometry,False,True,True,False),
            ('approachLine',QgsWkbTypes.LineGeometry,False,False,False,False),
            ('Segments',QgsWkbTypes.LineGeometry,False,False,False,False),
            ('population_fp',QgsWkbTypes.LineGeometry,False,True,False,False)
            ]
            
        volumes=[
            "grbMod",
            "cvMod",
            "fgMod",
            "low" # must stay last as it changes selection
            ]
            
        excelTypes = [
            'Targets',
            'AirRisk',
            'GroundRisk',
            'fp',
            'FlightArea',
            'Segments'
            ]
        
        COLUMN_ORDER = {
            'airrisk': ['id', 'name', 'descr', 'icao', 'iata', 'geoArea', 'coords', 'critical', 'phone', 'website', 'email', 'status','notes','email_notification'],
            'groundrisk': ['id', 'name', 'descr', 'geoArea','coords', 'critical', 'status','notes'],
            'fp': ['id', 'fmode', 'len', 'altitude', 'speed(m/s)', 'fg', 'fgAlt', 'cv', 'cvAlt', 'grb', 'status'],
            'targets': ['id', 'name', 'contact', 'phone', 'email', 'descr', 'address', 'coords', 'status','notes','coords_measured'],
            'segments': ['id', 'a', 'a_name','b','b_name', 'connection', 'segment_len','flight_time_min','populated_percent','populated_len','populated_time_min','populated_time_perweek','straight_line_len', 'deliveries_per_week', 'status','group','notes','road_km'],
            'rally': ['id', 'name','coords','notes'],
            'population_fp': ['id', 'len','sectionLen','status'],
        }


        # DESTINATION DIRECTORY
        if not os.path.exists(destpath): 
            os.mkdir(destpath)

        # FIND AND VALIDATE LAYERS
        if layer_input.geometryType() != QgsWkbTypes.LineGeometry:
            raise QgsProcessingException('ERROR: the selected input layer is invalid (not a lines vector layer)')

        layersAll = QgsProject.instance().mapLayers().values()
        
        # FIND LAYERS
        layers = []
        for layerType in layerTypes:
            layerSrcName = opnum + '_' + layerType[0]
            layersMatching = []
            for layer in layersAll:
                if layer.name().startswith(layerSrcName):
                    layersMatching.append(layer)
            if len(layersMatching) is 1:
                layers.append(layersMatching[0])
            if len(layersMatching) is 0 and layerType[2]:
                raise QgsProcessingException(f"ERROR: No layer named {layerSrcName} was found")
            
            elif len(layersMatching) > 1:
                for layer in layersMatching:
                    if layer.name() == layerSrcName:
                        layers.append(layer)
                    elif len(layer.name().split('_')) >= 3 and layerType[3]:
                        layers.append(layer)
                    # else: # conflict between AirRisk and AirRiskAreas
                    #     raise QgsProcessingException(f"ERROR: More than one layer named {layerSrcName} was found: {layersMatching}")

        if debug:
            print(f"Layers found:")
            for layer in layers:
                print(layer)
        
        # VALIDATE LAYER GEOMETRY TYPE AND FEATURES
        for layer in layers:
            type = NULL
            for layerType in layerTypes:
                if layerType[0] in layer.name():
                    type = layerType
                    if debug: print(f"type = {layerType}")
            if type == NULL:
                raise QgsProcessingException(f"ERROR: The layer {layer.name()} is invalid")
                if debug: print(f"ERROR: The layer {layer.name()} is invalid")
            if layer.geometryType() != type[1]:
                raise QgsProcessingException(f"ERROR: The mandatory layer {layer.name()} is invalid (geometry type {layer.geometryType()} is not {layerType[1]})")
            if layer.featureCount() == 0:
                layers.remove(layer)
                if debug: print(f"Removing layer {layer}, as it has zero features")
            if len(layer.name().split('_')) > 2: # remove backlog layers
                if layer.name().split('_')[2] == 'backlog':
                    layers.remove(layer)


            # set fgDelta and cvDelta default values if empty
            if layer.name().split('_')[1] == "fp":  # flight path layer
                if not layer.isEditable():
                    layer.startEditing()

                fg_field_idx = layer.fields().indexFromName('fgDelta')
                cv_field_idx = layer.fields().indexFromName('cvDelta')

                for feature in layer.getFeatures():
                    updated = False
                    attrs = {}

                    if fg_field_idx != -1 and feature['fgDelta'] is None:
                        attrs[fg_field_idx] = 25
                        updated = True

                    if cv_field_idx != -1 and feature['cvDelta'] is None:
                        attrs[cv_field_idx] = 25
                        updated = True

                    if updated:
                        layer.changeAttributeValues({feature.id(): attrs})

                layer.commitChanges()
            
            # check unnamed rally points
            if layer.name().split('_')[1] == "GroundRisk":
                # descr_field_idx = layer.fields().indexFromName('descr')
                # rallyletter_field_idx = layer.fields().indexFromName('rallyletter')

                for feature in layer.getFeatures():
                    if feature['descr'] == 'Rally' and feature['status'] == 'Active':
                        if debug: print(f"{layer.name()} {feature['descr']} {feature['rallyletter']}")
                        if feature['rallyletter'] == NULL:
                            raise ValueError(f"Found Rally points without name in {layer.name()}\nPlease assign a rallyletter to all rally points")




        # CLEAN ALL LAYERS
        if debug: print(f"\nCLEAN ALL LAYERS Generic tools")
        processing.run("script:genericTools", {
            'LAYERS': layers,
            'GEOM':True,
            'ID':True,
            'STYLE':True,
            'SAVESTYLE': False,
            'RATIO':False,
            'DEBUG': False,
            'SORT': 0,
            'RALLYNAMES': False,
            'FIELDS':False
            }
        )
        
        # Define the expression to select features
        if later:
            expression = "\"status\" IS NULL OR \"status\" NOT IN ('Hidden')"        
        else:
            expression = "\"status\" IS NULL OR \"status\" NOT IN ('Later', 'Hidden')"        

        # PREPARE TEMPORARY LAYERS TO EXCLUDE FEATURES FOR LATER OR HIDDEN
        if debug: print(f"\nPREPARE TEMPORARY LAYERS TO EXCLUDE FEATURES FOR LATER OR HIDDEN")
        layers_temp = []
        for layer in layers:
            # Select features based on the expression
            layer.removeSelection()
            layer.selectByExpression(expression)
            
            if layer.selectedFeatureCount() == 0:  # Check if there are any selected features
                if debug: print(f"WARNING: skipping {layer} as it has no suitable features")
                continue
            if debug: print(f"{layer} Selected features: {layer.selectedFeatureCount()}")
            
            # Prepare to create a temporary layer
            geom_type = layer.geometryType()
            if geom_type == QgsWkbTypes.PointGeometry:
                layer_type = "Point"
            elif geom_type == QgsWkbTypes.LineGeometry:
                layer_type = "LineString"
            elif geom_type == QgsWkbTypes.PolygonGeometry:
                layer_type = "Polygon"
            else:
                raise Exception(f"Unsupported geometry type {geom_type}")

            crs = layer.crs().authid()
            temp_layer = QgsVectorLayer(f"{layer_type}?crs={crs}", layer.name(), "memory")
            
            # Get the ratio of the original layer
            if debug: print(f"Get the ratio of the original layer {layer}")
            ratio_value = common.manage_layer_variable(layer, variable='ratio', write=False, debug=False)
            
            # Copy the fields from the original layer to the temporary layer
            temp_layer_data = temp_layer.dataProvider()
            attr = layer.dataProvider().fields()
           
            temp_layer_data.addAttributes(attr.toList())
            temp_layer.updateFields()

            # Copy the selected features to the new memory layer
            features = layer.selectedFeatures()
            
            # Validate the selected features and append to the temp layer
            for feature in features:
                try:
                    if not feature.hasGeometry() or feature.geometry().isEmpty():
                        if debug: print(f"ERROR: Skipping feature {feature['ID']} due to missing or empty geometry.")
                        continue
                    
                    # Check if the feature's geometry is compatible with the temporary layer's geometry type
                    if feature.geometry().type() != geom_type:
                        if debug: print(f"ERROR: Skipping feature {feature['ID']} due to incompatible geometry type.")
                        continue

                    # Apply sanitization to notes field
                    if 'notes' in feature.fields().names():
                        feature['notes'] = sanitize_notes(feature['notes'])

                    # Try to add the feature to the temporary layer
                    success = temp_layer_data.addFeature(feature)
                    if not success:
                        msg = f"ERROR: Failed to add feature with ID {feature['ID']} to {layer}."
                        print(msg)
                        feedback.pushInfo(msg)
                    
                except Exception as e:
                    if debug: print(f"ERROR:  processing feature {feature['ID']}: {e}")

            layers_temp.append(temp_layer)

            # Add the temporary layer to the project
            new_layer = QgsProject.instance().addMapLayer(temp_layer)
            
            # Apply the stored ratio
            if debug: print(f"apply the stored ratio to new_layer {new_layer}")
            common.manage_layer_variable(new_layer, variable='ratio', write=True, debug=False, ratio_value=ratio_value)
            
            layer.removeSelection()

            if debug: print(f"Temporary layer {temp_layer} created with {temp_layer.featureCount()} features / Filtered features: {len(features)}")

        layers = layers_temp

        if debug:
            print(f"Updated Layers list:")
            for layer in layers:
                print(layer)

        # STYLE TEMP LAYERS
        if debug: print(f"Styling temp layers: {layers}")
        processing.run("script:genericTools", {
            'LAYERS': layers,
            'GEOM':False,
            'ID':False,
            'STYLE':True,
            'SAVESTYLE': False,
            'RATIO':False,
            'DEBUG': False,
            'SORT': 0,
            'RALLYNAMES': False
            }
        )






        #########################
        ######## VOLUMES ########
        #########################
        
        layer_volumes = []
        daa_buffers = []
        
        #if parameters['BUFFER']:
        if self.parameterAsBool(parameters, self.BUFFER, context):
            # FIND LAYERS THAT NEED BUFFERING
            layersBuffer = []
            for layer in layers:
                type = NULL
                for layerType in layerTypes:
                    if layerType[0] in layer.name():
                    #if layer.name().split('_')[1] == layerType[0]:
                        type = layerType
                if type[4]: # check if the layer type needs buffering
                    layersBuffer.append(layer)
            if debug: print(f"layersBuffer: {layersBuffer}")
            
            # VALIDATE RATIO
            if debug: print(f"VALIDATE RATIO")
            for layer in layersBuffer:
                ratio_value = common.manage_layer_variable(layer,variable='ratio',write=False,debug=False,ratio_value=1)
                
                if ratio_value == 0 or ratio_value == NULL or ratio_value == '':
                    raise QgsProcessingException(f"ERROR: {layer} has invalid ratio: {ratio_value}")
                elif ratio_value == 1:
                    if debug: print(f"WARNING: {layer} ratio is: {ratio_value}")
                    feedback.pushInfo(f"WARNING: {layer} ratio is: {ratio_value}")
            if debug: print(f"ratio_value {ratio_value}")
            
            # CREATE TARGET FOLDER
            destpathKml = os.path.join(destpath,'KML')
            if not os.path.exists(destpathKml):
                os.mkdir(destpathKml)

            # BUFFER LAYERS
            if debug: print(f"\nCREATING VOLUMES")
            bufferedLayers = []
            lowLayers = []
            if debug: print(f"Buffering layers")
            for layer in layersBuffer:
                bufferLayer(layer, volumes, destpathKml, debug)
            
            # MERGE VOLUMES
            layer_volumes = mergeVolumes()

            
            # GENERATE DAA LAYER
            if debug: print(f"\nGENERATE DAA LAYER")
            fg_feature = None
            for feat in layer_volumes.getFeatures():
                if feat['type'].lower() == 'fg':
                    fg_feature = feat
                    break

            if fg_feature is None:
                raise QgsProcessingException("No FG feature found in volumes – cannot generate DAA buffers")
            #else:
            #    if debug: print("FG feature geometry size:", fg_feature.geometry().area(), fg_feature.geometry().length())


            if fg_feature is not None:
                geom = fg_feature.geometry()
                if geom.isEmpty():
                    raise QgsProcessingException("ERROR: FG feature has empty geometry. Cannot generate DAA layer.")

                daa_buffers = []
                
                # Step 1: Extract FG feature layer
                fg_layer_raw = processing.run("native:extractbyexpression", {
                    'INPUT': layer_volumes,
                    'EXPRESSION': "\"type\" = 'fg'",
                    'OUTPUT': 'memory:'
                })['OUTPUT']

                # Reproject to project CRS if needed
                project_crs = QgsProject.instance().crs()
                if debug:
                    print(f"project CRS {project_crs}")
                    print(f"fg_layer_raw.crs {fg_layer_raw.crs()}")
                if fg_layer_raw.crs() != project_crs:
                    fg_layer = processing.run("native:reprojectlayer", {
                        'INPUT': fg_layer_raw,
                        'TARGET_CRS': project_crs,
                        'OUTPUT': 'memory:fg_reprojected'
                    })['OUTPUT']
                else:
                    fg_layer = fg_layer_raw
                if debug: print(f"pfg_layer {fg_layer}")
                    
                # Step 2: Validate FG layer
                if fg_layer.featureCount() == 0:
                    raise QgsProcessingException("No FG feature extracted for DAA buffers")
                    
                # Step 3: Buffer 5 times (1km * ratio)
                for i in range(1, 6):
                    # if debug: print(f"ratio_value {ratio_value}")
                    distance = 1000 * float(ratio_value) * i
                    if debug: print(f"DAA buffer distance: {distance}")
                    result = processing.run("native:buffer", {
                        'INPUT': fg_layer,
                        'DISTANCE': distance,
                        'OUTPUT': 'memory:',
                        'SEGMENTS': 20,
                        'DISSOLVE': False
                    })

                    buf_layer = result['OUTPUT']
                    
                    buf_layer.startEditing()
                    idx_type = findField(buf_layer, 'type')
                    for feat in buf_layer.getFeatures():
                        buf_layer.changeAttributeValue(feat.id(), idx_type, f"daa")
                    buf_layer.commitChanges()
                    
                    daa_buffers.append(buf_layer)
                
                # add daa layers to project
                if debug: print("add daa layers to project")
                for i, buf_layer in enumerate(daa_buffers):
                    if debug: print(f"DAA buffer {i}: {buf_layer.name()}, feature count = {buf_layer.featureCount()}")
                    buf_layer.setName(f"{opnum}_daa_{i}")
                    # QgsProject.instance().addMapLayer(buf_layer, False) # add them silently
                    QgsProject.instance().addMapLayer(buf_layer)
                

                # Remove fid fields from buffer layers before merging
                for lyr in daa_buffers:
                    common.remove_fid_field(lyr)  # This should be done BEFORE mergevectorlayers
                    common.remove_all_fields_but(lyr, ['type'])

                # Remove old DAA file if needed
                outputname = opnum + '_daa'
                input_path  = os.path.dirname(layer_input.dataProvider().dataSourceUri().split('|')[0])
                output = os.path.join(input_path , outputname + '.gpkg')
                if debug: print(f"Remove old DAA file: {output}")
                
                checkOverwrite(output) # throw an error if the parameter overwrite is false and overwriting is needed
                if self.parameterAsBool(parameters, self.OVERWRITE, context):
                    for layer in QgsProject.instance().mapLayersByName(outputname):
                        if debug: print(f"removing from project {layer}")
                        QgsProject.instance().removeMapLayer(layer.id())
                
                # merge daa buffer layers
                if debug: print("merge daa buffer layers")

                # Run the merge to a temporary layer
                result = processing.run("native:mergevectorlayers", {
                    'LAYERS': daa_buffers,
                    'CRS': QgsCoordinateReferenceSystem('EPSG:3857'),
                    'OUTPUT': 'TEMPORARY_OUTPUT'
                }, context=context)

                # Write to the desired output manually
                temp_layer = result['OUTPUT']
                try:
                    err, msg = QgsVectorFileWriter.writeAsVectorFormat(temp_layer, output, 'utf-8', driverName='GPKG')
                    if err == QgsVectorFileWriter.NoError:
                        if debug: print(f"DAA buffer written successfully to {output}")
                        layer_daa = iface.addVectorLayer(output, outputname,'ogr')
                        QgsProject.instance().addMapLayer(layer_daa, False)
                        style = os.path.join(stylespath, 'daa.qml')
                        layer_daa.loadNamedStyle(style) # layer styling
                        layer_daa.triggerRepaint() # layer styling repaint
                        layers.append(layer_daa)
                        if debug: print(f"daa layer added {layer_daa.name()}")
                    else:
                        print(f"ERROR writing DAA layer: {msg} (Error code: {err})")
                        feedback.reportError(f"ERROR writing DAA layer {msg}")
                except Exception as e:
                    raise QgsProcessingException(f"Exception during DAA layer write: {e}")
            else:
                if debug: print("No FG feature found in volumes. Skipping DAA layer generation.")

            
            # EXPORT INDIVIDUAL SECTIONS
            if debug: print(f"\nEXPORT INDIVIDUAL SECTIONS {layersBuffer}")
            for layer in layersBuffer:
                exportSections(layer)

            # MERGE LOW LAYERS
            if lowLayers and isinstance(lowLayers, list) and len(lowLayers) > 0:
                layer_low = mergeLow()
            else:
                layer_low = None
                if debug: print("No Low flight areas found, skipping")





        #########################
        ######  EXPORT KMZ ######
        #########################
        if self.parameterAsBool(parameters, self.KMZ, context):
            if debug: print(f"\nEXPORT KMZ")
            if debug:
                for layer in layers:
                    print(layer)
            
            # Delete all .kmz files in the directory
            if self.parameterAsBool(parameters, self.OVERWRITE, context):
                common.delete_by_extension(destpath,'kmz',debug)            
            
            for current, layer in enumerate(layers):
                if layer.featureCount() == 0:
                    if debug: print(f"Exporting skipped: layer has no features {layer.name()}")
                    continue
                dirty = False
                if feedback.isCanceled():
                    break
                if debug: print(f"# Processing {layer.name()}")
                

                '''
                if layer.name().split('_')[1] != 'Volumes':
                    print("DEBUG skip")
                    continue
                '''
                
                # special case for AirRiskAreas and approachLine (not sure it is needed)
                if layer.name().split('_')[1] == 'AirRiskAreas' or layer.name().split('_')[1] == 'approachLine':
                    if debug: print(f"EXPORT KMZ Exporting AirRiskAreas layer as KML {layer.name()}")
                    targetFile = destpath + '/KML/' + layer.name() + '.kml'
                    checkOverwrite(targetFile)
                    
                    options = QgsVectorFileWriter.SaveVectorOptions()
                    options.driverName = "KML"
                    options.fileEncoding = 'utf-8'
                    QgsVectorFileWriter.writeAsVectorFormatV3(layer, targetFile, QgsProject.instance().transformContext(), options)
                    # continue

                # check if this layer type requires KMZ export
                layer_type_name = layer.name().split('_')[1]
                layerType_kmz = next((lt for lt in layerTypes if lt[0] == layer_type_name), None)
                need_kmz = layerType_kmz[5] if layerType_kmz else False

                if (not need_kmz) and (layer_type_name != 'Volumes'):
                    if debug: print(f"KMZ not required, skipping {layer.name()}")
                    continue

                # VALIDATE STYLING
                if debug: print(f"validating style {layer}")
                if layer.renderer():
                    if layer.renderer().type() == 'categorizedSymbol' or layer.renderer().type() == 'singleSymbol':
                        if debug: print(f"style ok")
                    else:
                        if debug: print(f"WARNING: The layer does not have categorized styling. Skipping to next layer")
                        feedback.pushInfo("WARNING: The layer does not have categorized styling. Skipping to next layer")
                        continue
                else:
                    if debug: print(f"WARNING: The layer does not have a renderer set. Skipping to next layer")
                    feedback.pushInfo("WARNING: The layer does not have a renderer set. Skipping to next layer")
                    continue
                
                # VALIDATE AND FIX FIELDS
                if debug: print(f"validating and fixing fields {layer}")
                fields = [field.name() for field in layer.fields()] # field names list
                fieldsLower = [field.name().lower() for field in layer.fields()] # field names list CASE UNSENSITIVE

                fieldsNeeded = ['name', 'folder']
                for field_name in fieldsNeeded:
                    if field_name not in fieldsLower:
                        layer.startEditing()
                        layer.addAttribute(QgsField(field_name, QVariant.String, len=100))
                        layer.commitChanges()
                        if debug: print(f"Added missing field {field_name}")
                
                
                # FOLDER ATTRIBUTE
                # if debug: print(f"\nFOLDER ATTRIBUTE {layer} - {layer.featureCount()} features")
                layer.startEditing()
                for feature in layer.getFeatures():
                    if layer.name().split('_')[1] == 'fp':
                        value = feature['id']
                    else:
                        value = layer.name().split('_')[1]
                    #if debug: print(f"Feature ID: {feature['id']} value: {value}")
                    
                    layer.changeAttributeValue(
                        feature.id(),
                        findField(layer, fieldsNeeded[1]),
                        value
                        )
                try:
                    layer.commitChanges()
                    if debug: print(f"committed folder attribute changes on {layer}")
                except Exception as e:
                    msg = f"ERROR: failed to commit folder attribute changes on {layer}"
                    print(msg)
                    feedback.reportError(msg)
                    layer.rollBack()
                
                
                # NAME ATTRIBUTE
                layer.startEditing()
                # if debug: print(f"\nNAME ATTRIBUTE {layer} - {layer.featureCount()} features")
                for feature in layer.getFeatures():
                    if feature['name'] == NULL:
                        layer.changeAttributeValue(
                            feature.id(),
                            findField(layer, fieldsNeeded[0]),
                            feature['id']
                            )
                try:
                    layer.commitChanges()
                    if debug: print(f"committed name attribute changes on {layer}")
                except Exception as e:
                    msg = f"ERROR: failed to commit name attribute changes on {layer}"
                    print(msg)
                    feedback.reportError(msg)
                    layer.rollBack()
                
                layer, dirty = reproject(layer,target_crs,True)

                # CONFIG EXPORTER
                fields = [field.name() for field in layer.fields()] # field names list
                if 'Name' in fields:
                    name = 'Name'
                elif 'name' in fields:
                    name = 'name'
                else:
                    raise QgsProcessingException(f"ERROR: the name field is missing on the layer {layer.name()}")
                folder = 'folder' # folder field
                targetFile = destpath + '/' + layer.name() + '.kmz'
                checkOverwrite(targetFile)

                # RUN EXPORTER
                if debug: print(f"Exporting layer: {layer.name()} - geometry type: {layer.geometryType()}")
                kml_processor = exporter.kmlprocess(layer,name,folder,fields,True,targetFile,None)
                # kml_processor.process()
                try:
                    kml_processor.process()
                except Exception as e:
                    feedback.reportError(f"ERROR KMZ export failed for {layer.name()}: {e}")
                    print(f"ERROR KMZ export failed for {layer.name()}: {e}")                
                #kml_processor = exporter.kmlprocess(layer,name,folder,fields,True,targetFile,None, debug)
                #kml_processor.process(debug)
                feedback.pushInfo(f"EXPORTING in KMZ to {targetFile}")
                if debug: print(f"waiting 1s")
                time.sleep(1)

                if dirty:
                    if debug: print(f"Removing temporary layer {layer.name()}")
                    QgsProject.instance().removeMapLayer(layer.id())

            # GENERATE FP FOR SEGMENTS
            if debug: print("\nGENERATE FP FOR SEGMENTS")
            layer_segments = None
            layer_fp = None

            for layer in layers:
                if layer.name().split('_')[1] == "Segments":
                    layer_segments = layer
                    if debug: print(f"Segment layer found: {layer_segments}")
                elif layer.name().split('_')[1] == "fp":
                    layer_fp = layer
                    if debug: print(f"Fp layer found: {layer_fp}")

            if layer_segments and layer_fp:  # Check if both layers are found
                for feature in layer_segments.getFeatures():
                    if debug: print(f"segment id {feature['id']}")
                    connection_str = feature['connection']
                    if not connection_str:
                        if debug: print(f"Skipping segment {feature['id']} due to empty connection field")
                        continue
                    if debug: print(f"connection_str {connection_str}")

                    connection_fp_ids = [id_str.strip() for id_str in connection_str.split() if id_str.strip()]
                    if not connection_fp_ids:
                        if debug: print(f"Skipping segment {feature['id']} due to invalid connection IDs")
                        continue
                    if debug: print(f"connection_fp_ids {connection_fp_ids}")

                    # Build expression for selecting features from fp layer
                    expr = '"id" IN ({})'.format(", ".join("'{}'".format(id_) for id_ in connection_fp_ids))
                    layer_fp.selectByExpression(expr, QgsVectorLayer.SetSelection)

                    if layer_fp.selectedFeatureCount() == 0:
                        if debug: print(f"No matching fp features found for segment {feature['id']}")
                        continue

                    # Define output path
                    output_filename = f"{opnum}_segmentfp_{feature['id']}.kml"
                    output_path = os.path.join(destpath,'KML', output_filename)
                    checkOverwrite(output_path)

                    if debug: print(f"Dissolving selected fp features for segment {feature['id']} into {output_path}")

                    result = processing.run("native:dissolve", {
                        'INPUT': QgsProcessingFeatureSourceDefinition(layer_fp.id(), selectedFeaturesOnly=True),
                        'FIELD': [],
                        'OUTPUT': output_path
                    }, context=context)

                    layer_fp.removeSelection()
            else:
                if debug: print("Could not find valid 'Segments' and 'fp' layers. Skipping segment FP generation.")





        #####################################
        ############## EXCEL ################
        #####################################
        
        try:
            if self.parameterAsBool(parameters, self.EXCEL, context):
                if debug: print(f"\nEXPORTING EXCEL FILES")

                for layer in layers:
                    if debug: print(f"Processing {layer}")
                    if feedback.isCanceled():
                        break
                    #if layer.name().split('_')[1].lower() in COLUMN_ORDER.keys():
                    if "_".join(layer.name().split('_')[1:]).lower() in COLUMN_ORDER.keys():
                        feedback.pushInfo(f"Excel exporting: '{layer.name()}'")
                    else:
                        continue

                    targetFile = os.path.join(destpath, f"{layer.name()}.xlsx")

                    # Ensure old files are deleted before writing new ones
                    if os.path.exists(targetFile):
                        os.remove(targetFile)

                    options = QgsVectorFileWriter.SaveVectorOptions()
                    options.driverName = "XLSX"
                    options.fileEncoding = 'utf-8'
                    if debug: print(f"📂 Exporting file: {targetFile}")
                    QgsVectorFileWriter.writeAsVectorFormatV3(layer, targetFile, QgsProject.instance().transformContext(), options)

                
                # MERGE EXPORTED EXCEL FILES
                if debug: print(f"Merging exported Excel files")
                population_fp_written = False

                # Define path for merged file
                merged_file = os.path.join(destpath, f"{opnum}_omData.xlsx")

                # Filter only the Excel files listed in COLUMN_ORDER
                valid_sheet_names = set(COLUMN_ORDER.keys())
                excel_files = []
                for f in os.listdir(destpath):
                    if f.endswith('.xlsx'):
                        file_parts = os.path.splitext(f)[0].split('_')  # Split filename without extension
                        if len(file_parts) > 1:
                            sheet_name = "_".join(file_parts[1:]).lower()  # Join all parts after the first
                            if sheet_name in valid_sheet_names:
                                excel_files.append(f)

                if debug: print("Filtered Excel files:", excel_files)
                
                
                if excel_files:
                    if not is_file_writable(merged_file): # this should probably be moved, doesn't work
                        feedback.reportError(f"ERROR: Cannot write to Excel file: {merged_file}. Skipping omData export.")
                    else:
                        # with pd.ExcelWriter(merged_file, engine='openpyxl') as writer:
                        with pd.ExcelWriter(merged_file, engine='xlsxwriter') as writer:
                            empty = True  # Track if any sheets are written

                            for file in excel_files:
                                file_path = os.path.join(destpath, file)
                                # sheet_name = os.path.splitext(file)[0].split('_')[1].lower()  # Extract and lowercase type
                                sheet_name = "_".join(os.path.splitext(file)[0].split('_')[1:]).lower() # Extract and lowercase type

                                # Verify file exists and is non-empty
                                if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
                                    feedback.pushInfo(f"WARNING: Skipping empty or missing file: {file_path}")
                                    continue

                                # Check if the file is a valid ZIP (Excel .xlsx is a ZIP archive)
                                try:
                                    with zipfile.ZipFile(file_path) as zf:
                                        if zf.testzip() is not None:
                                            raise zipfile.BadZipFile
                                except zipfile.BadZipFile:
                                    feedback.pushInfo(f"WARNING: Skipping corrupt Excel file: {file_path}")
                                    continue

                                # Read Excel file safely
                                try:
                                    df = pd.read_excel(file_path, engine='openpyxl')
                                except Exception as e:
                                    feedback.reportError(f"ERROR: Could not read {file_path}: {e}")
                                    continue

                                if df.empty:
                                    feedback.pushInfo(f"WARNING: {file_path} is empty. Skipping.")
                                    continue

                                # Check if Excel file is writable
                                try:
                                    wb = load_workbook(file_path)
                                    test_sheet = "__writetest__"
                                    wb.create_sheet(test_sheet)
                                    wb.remove(wb[test_sheet])
                                    wb.save(file_path)
                                    wb.close()
                                except (PermissionError, InvalidFileException, OSError) as e:
                                    feedback.reportError(f"ERROR: File not writable: {file_path}: {e}")
                                    continue
                                
                                empty = False  # At least one sheet has data

                                # Standardize column names to lowercase and remove spaces
                                df.columns = [col.strip().lower() for col in df.columns]

                                # Ensure required columns exist (case-insensitive lookup)
                                required_columns = [col.lower() for col in COLUMN_ORDER.get(sheet_name, [])]

                                # Map required columns to actual DataFrame column names
                                column_mapping = {col: col for col in df.columns}  # Direct match
                                for col in required_columns:
                                    if col not in column_mapping:
                                        matches = [actual_col for actual_col in df.columns if actual_col.lower() == col]
                                        if matches:
                                            column_mapping[col] = matches[0]  # Use the first match

                                # Apply the filtered column selection (if available)
                                filtered_columns = [column_mapping[col] for col in required_columns if col in column_mapping]
                                df = df[filtered_columns] if filtered_columns else df

                                # Convert 'critical' column to lowercase string if present
                                if 'critical' in df.columns:
                                    df['critical'] = df['critical'].astype(str).str.lower()

                                # Sort by 'id' and then 'descr' if they exist
                                sort_columns = [col for col in ['id', 'descr'] if col in df.columns]
                                if sort_columns:
                                    df = df.sort_values(by=sort_columns)

                                # Separate critical items if applicable
                                if 'critical' in df.columns:
                                    df_critical = df[df['critical'] == 'yes']
                                    df_non_critical = df[df['critical'] != 'yes']

                                    # Ensure both DataFrames have same columns
                                    all_cols = df.columns

                                    if df_critical.empty:
                                        # df_critical = pd.DataFrame([["" for _ in all_cols]], columns=all_cols)
                                        df_critical = pd.DataFrame([["No critical data"] + [""] * (len(all_cols) - 1)], columns=all_cols)

                                    if df_non_critical.empty:
                                        df_non_critical = pd.DataFrame([["" for _ in all_cols]], columns=all_cols)

                                    df_critical.to_excel(writer, sheet_name=f"{sheet_name}_critical", index=False)
                                    df_non_critical.to_excel(writer, sheet_name=sheet_name, index=False)

                                else:
                                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                                    if sheet_name == 'population_fp':
                                        population_fp_written = True

                            # If no data was written, create a placeholder sheet
                            if empty:
                                feedback.pushInfo("WARNING: No valid data found, creating an empty Excel file.")
                                pd.DataFrame({"Notice": ["No data available"]}).to_excel(writer, sheet_name="Empty", index=False)

                    # Load workbook to apply formatting
                    wb = load_workbook(merged_file)
                    if not hasattr(wb, 'calculation_properties') or wb.calculation_properties is None:
                        wb.calculation_properties = CalcProperties()

                    wb.calculation_properties.fullCalcOnLoad = True
                    
                    for sheet_name in wb.sheetnames:
                        ws = wb[sheet_name]

                        if ws.max_row > 1:  # Only format if there's actual data
                            # Convert range to table format
                            min_col, max_col = 1, ws.max_column
                            min_row, max_row = 1, ws.max_row
                            table_range = f"A{min_row}:{ws.cell(row=max_row, column=max_col).coordinate}"
                            table = Table(displayName=f"Table_{sheet_name}", ref=table_range)
                            table.tableStyleInfo = TableStyleInfo(
                                name="TableStyleMedium9",
                                showFirstColumn=False,
                                showLastColumn=False,
                                showRowStripes=True,
                                showColumnStripes=False,
                            )
                            ws.add_table(table)

                        # Apply font settings
                        for row in ws.iter_rows():
                            for cell in row:
                                cell.font = Font(name="Calibri", size=8)

                    wb.save(merged_file)
                    wb.close()
                    feedback.pushInfo(f"Excel exporting: {merged_file}")
                    
                    
                    # Extract Rally Points from Ground Risk Data
                    rally_data = []

                    for file in excel_files:
                        file_path = os.path.join(destpath, file)
                        sheet_name = os.path.splitext(file)[0].split('_')[1].lower()

                        if sheet_name == 'groundrisk':  # Process only ground risk sheets
                            try:
                                df = pd.read_excel(file_path, engine='openpyxl')
                                df.columns = [col.strip().lower() for col in df.columns]  # Normalize column names

                                if 'descr' in df.columns:
                                    df_rally = df[df['descr'].str.lower() == 'rally']  # Filter only 'Rally' entries

                                    if not df_rally.empty:
                                        # Select only required columns
                                        rally_columns = COLUMN_ORDER['rally']
                                        column_mapping = {col: col for col in df_rally.columns}
                                        for col in rally_columns:
                                            if col not in column_mapping:
                                                matches = [actual_col for actual_col in df_rally.columns if actual_col.lower() == col]
                                                if matches:
                                                    column_mapping[col] = matches[0]

                                        filtered_columns = [column_mapping[col] for col in rally_columns if col in column_mapping]
                                        df_rally = df_rally[filtered_columns] if filtered_columns else df_rally

                                        rally_data.append(df_rally)

                            except Exception as e:
                                feedback.reportError(f"ERROR processing {file_path} for rally points: {e}")
                                continue

                    if rally_data:
                        rally_df = pd.concat(rally_data, ignore_index=True)
                        rally_file = os.path.join(destpath, f"{opnum}_RallyPoints.xlsx")

                        with pd.ExcelWriter(rally_file, engine='openpyxl') as rally_writer:
                            rally_df.to_excel(rally_writer, sheet_name="RallyPoints", index=False)

                        feedback.pushInfo(f"Excel exporting: {rally_file}")
                    else:
                        feedback.pushInfo("No rally points found in ground risk data.")
        except Exception as e:
            msg = f"ERROR: Unhandled exception during Excel export: {e}"
            print(msg)
            feedback.pushInfo(msg)




        #########################
        ######## GEOJSON ########
        #########################

        #if parameters['GEOJSON']:
        if self.parameterAsBool(parameters, self.GEOJSON, context):
            if debug: print(f"\nGEOJSON")
            geojsonPath = os.path.join(destpath, 'MAPBOX')
            if not os.path.exists(geojsonPath):
                os.mkdir(geojsonPath)
            
            '''
            # Delete all .geojson files in the directory
            if parameters['OVERWRITE']:
                for file_path in glob.glob(os.path.join(geojsonPath, '*.geojson')):
                    try:
                        os.remove(file_path)
                        if debug: print(f"Deleted: {file_path}")
                    except Exception as e:
                        print(f"Failed to delete {file_path}: {e}")
            '''
            # Delete all .geojson files in the directory
            #if parameters['OVERWRITE']:
            if self.parameterAsBool(parameters, self.OVERWRITE, context):
                common.delete_by_extension(geojsonPath,'geojson',debug)
           
            layers_geojson_export = []
            fields_to_keep = ["name", "id", "descr", "critical", "type"]

            layers_points = []
            layers_polys = []
            layers_lines = []

            layers_names_points = ["targets", "groundrisk", "airrisk"]
            layers_names_polys = ["airriskareas", "daa", "volumes"]
            layers_names_lines = ["approachline"]

            for layer in layers:
                lname_parts = layer.name().split('_')
                if len(lname_parts) < 2:
                    continue
                layer_type = lname_parts[1].lower()
                if layer_type in layers_names_points:
                    layers_points.append(layer)
                elif layer_type in layers_names_polys:
                    layers_polys.append(layer)
                elif layer_type in layers_names_lines:
                    layers_lines.append(layer)

            
            def merge_layers(layers_list, geom_type, crs):
                if debug:
                    print("\nmerge_layers")
                    print(f"layers_list {layers_list}")
                    print(f"geom_type {geom_type}")
                    print(f"crs {crs}")
                    
                temp_layer = QgsVectorLayer(f"{geom_type}?crs={crs.authid()}", f"{opnum}_{geom_type}", "memory")
                temp_provider = temp_layer.dataProvider()
                all_fields = QgsFields()

                reprojected_layers = []
                for layer in layers_list:
                    if layer.crs() != crs:
                        layer, _ = reproject(layer, crs, False)
                        layers_temp.append(layer)
                    reprojected_layers.append(layer)
                    for f in layer.fields():
                        if f.name() not in all_fields.names():
                            all_fields.append(f)
                
                temp_provider.addAttributes(all_fields)
                temp_layer.updateFields()

                for layer in reprojected_layers:
                    for feat in layer.getFeatures():
                        new_feat = QgsFeature(all_fields)
                        new_feat.setGeometry(feat.geometry())
                        for f in layer.fields():
                            new_feat[f.name()] = feat[f.name()]
                        temp_provider.addFeature(new_feat)

                temp_layer.updateExtents()
                
                if debug: print(f"merge output temp_layer {temp_layer}")
                return temp_layer

            def filter_and_clean_layer(layer, fields_to_keep, status_field='status', status_value='Active'):
                temp_layer = QgsVectorLayer(f"{QgsWkbTypes.displayString(layer.wkbType())}?crs={layer.crs().authid()}",
                                            layer.name(), "memory")
                                            #layer.name() + "_filtered", "memory")
                temp_layer_data = temp_layer.dataProvider()
                kept_fields = [f for f in layer.fields() if f.name().lower() in [k.lower() for k in fields_to_keep]]
                temp_layer_data.addAttributes(kept_fields)
                temp_layer.updateFields()

                for feat in layer.getFeatures():
                    if status_field in feat.fields().names() and feat[status_field] != status_value:
                        continue
                    new_feat = QgsFeature(temp_layer.fields())
                    new_feat.setGeometry(feat.geometry())
                    new_feat.setAttributes([feat[attr.name()] for attr in temp_layer.fields()])
                    temp_layer_data.addFeature(new_feat)

                temp_layer.updateExtents()
                return temp_layer

            # Merge and process points
            if layers_points:
                merged_points = merge_layers(layers_points, "Point", QgsCoordinateReferenceSystem('EPSG:3857'))
                filtered_points = filter_and_clean_layer(merged_points, fields_to_keep)
                layers_geojson_export.append(filtered_points)

            # Merge and process polys
            if layers_polys:
                merged_polys = merge_layers(layers_polys, "Polygon", QgsCoordinateReferenceSystem('EPSG:3857'))
                filtered_polys = filter_and_clean_layer(merged_polys, fields_to_keep)
                layers_geojson_export.append(filtered_polys)

            # Add lines directly
            if layers_lines:
                layers_geojson_export.append(layers_lines[0])

            # Export
            for layer in layers_geojson_export:
                if debug: print(f"\nExporting {layer}")
                if feedback.isCanceled():
                    break
                if layer.name().split('_')[1] == 'fp':
                    if debug: print("skipping because fp")
                    continue

                if layer.name().split('_')[1] == 'approachLine':
                    target_name = 'Lines'
                elif layer.name().split('_')[1] == 'Point':
                    target_name = 'Points'
                elif layer.name().split('_')[1] == 'Polygon':
                    target_name = 'Polys'
                else:
                    raise QgsProcessingException(f"ERROR: target_name is bad as layer.name().split('_')[1] is {layer.name().split('_')[1]}")
                target_name = opnum + '_' + target_name + '.geojson'

                targetFile = os.path.join(geojsonPath, target_name)
                checkOverwrite(targetFile)
                if debug: print(f"GeoJSON export Target file: {targetFile}")

                layer, dirty = reproject(layer, target_crs, False)

                options = QgsVectorFileWriter.SaveVectorOptions()
                options.driverName = "GEOJSON"
                options.fileEncoding = 'utf-8'

                QgsVectorFileWriter.writeAsVectorFormatV3(layer, targetFile, QgsProject.instance().transformContext(), options)

                if dirty:
                    if debug: print(f"Removing temporary layer {layer.name()}")
                    QgsProject.instance().removeMapLayer(layer.id())





        #########################
        ######## CLEANUP ########
        #########################
        
        if self.parameterAsBool(parameters, self.CLEANUP, context):
            if debug:print("\nCLEANUP")
            
            # Combine all layers to check for deletion
            if daa_buffers:
                layers_temp.extend(daa_buffers)

            # Build list of IDs to keep
            layers_keep_ids = [layer.id() for layer in [layer_volumes, layer_low] if layer]

            for layer in layers_temp:
                if layer and layer.id() not in layers_keep_ids:
                    if debug:
                        print(f"Removing temporary layer {layer}")
                    QgsProject.instance().removeMapLayer(layer.id())
                else:
                    if debug:
                        print(f"Not removing {layer if layer else 'None'}")

        
        # redraw the canvas
        iface.mapCanvas().refresh()
        gc.collect()
        
        return {}