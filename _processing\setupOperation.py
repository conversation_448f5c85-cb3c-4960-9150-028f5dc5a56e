from qgis.PyQt.QtCore import QCoreApplication
from qgis.core import (QgsProcessing,
                       QgsFeatureSink,
                       QgsProcessingException,
                       QgsProcessingAlgorithm,
                       QgsProcessingParameterFeatureSource,
                       QgsProcessingParameterFeatureSink,
                       QgsProcessingParameterNumber,
                       QgsProcessingParameterString,
                       QgsProcessingParameterBoolean,
                       QgsCoordinateReferenceSystem,
                       QgsField,
                       QgsPoint,
                       QgsProject,
                       QgsVectorFileWriter,
                       QgsVectorLayer,
                       QgsGeometry,
                       QgsFeature,
                       QgsFeatureRequest,
                       QgsExpressionContextUtils,
                       QgsWkbTypes,
                       QgsSpatialIndex,
                       NULL)
from qgis import processing
from qgis import os
from qgis.PyQt.QtWidgets import QAction, QMessageBox, QFileDialog
from qgis.utils import iface
from qgis.PyQt.QtCore import QVariant
from collections import defaultdict


# IMPORT COMMON SCRIPTS
import importlib.util
import sys
module_path = os.path.join(os.path.dirname(QgsProject.instance().fileName()), '_common','_processing','common.py')
spec = importlib.util.spec_from_file_location("common", module_path)
common = importlib.util.module_from_spec(spec)
sys.modules["common"] = common
spec.loader.exec_module(common)


class ProjectSetup(QgsProcessingAlgorithm):
   
    OPID = 'OPID'
    OVERWRITE = 'OVERWRITE'
    DEBUG = 'DEBUG'
    
    def tr(self, string):
        return QCoreApplication.translate('Processing', string)
    def createInstance(self):
        return ProjectSetup()
    def name(self):
        return 'setupOp'
    def displayName(self):
        return self.tr('Setup Operation')
    def group(self):
        return self.tr('Jedsy scripts')
    def groupId(self):
        return 'jedsyscripts'
    def shortHelpString(self):
        return self.tr("Script used to setup a new Operation. \n\n CAUTION: if overwite is active it will overwrite existing files in the target directory")

    def flags(self):
        return QgsProcessingAlgorithm.FlagNoThreading
    
    def initAlgorithm(self, config=None):
        
        self.addParameter(
            QgsProcessingParameterString(
                name = self.OPID,
                description='Operation ID',
                defaultValue = '999_Location_Customer',
                #defaultValue = '999_test_test',
                multiLine = False,
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                name = self.OVERWRITE,
                description='Overwrite existing files\n(CAUTION!)',
                defaultValue = bool(0)
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.DEBUG,
                self.tr('Debug mode'),
                defaultValue = bool(0)
            )
        )
        
    def processAlgorithm(self, parameters, context, feedback):
    
        def findField(layer, name):  # Find field by name (case insensitive)
            for field in layer.fields():
                if field.name().lower() == name.lower():
                    return layer.fields().indexFromName(field.name())
            return -1  # Returns -1 if the field is not found
        
        # CONFIG
        debug = parameters['DEBUG']
        opid = self.parameterAsString(parameters, self.OPID, context)
        if opid == '999_Location_Customer':
            raise QgsProcessingException(f"ERROR: please set the operation id")
        opnum = opid.split('_')[0]
        layers = []
        
        root = QgsProject.instance().layerTreeRoot()
        projectpath = os.path.dirname(QgsProject.instance().fileName())
        oppath = os.path.join(projectpath, opid)
        stylespath = os.path.join(projectpath, '_common')
        templatesPath = os.path.join(projectpath, '000_Templates')

        if not os.path.exists(oppath): # create directory if it doesnt exist
            os.mkdir(oppath)
        
        # Get all templates gpkg files from templates folder
        gpkg_files = [file for file in os.listdir(templatesPath) if file.endswith('.gpkg') and file != '000_Targets.gpkg']
        
        # Export each of the gpkg templates in the destination folder and apply style
        for gpkg_file in gpkg_files:
            templatePath = os.path.join(templatesPath, gpkg_file)
            layerType = gpkg_file.split('_')[1].split('.')[0] # name of the layer without prefix or extension
            newlayername =  opnum + '_' + layerType
            layer = QgsVectorLayer(templatePath, newlayername, 'ogr')
            
            layers.append(
                common.export_layer(
                    input_layer=layer,
                    opnum=opnum,
                    overwrite=parameters['OVERWRITE'],
                    driverName="GPKG",
                    add_layer=True,
                    apply_style=True,
                    destpath=oppath,
                    debug=debug
                    )
                )
        

        # create TARGETS layer from targetsbriefing
        if debug: print('\n\nTARGETS LAYER')
        newlayername =  opnum + '_Targets'
        targetpath = os.path.join(oppath, newlayername + '.gpkg')
        
        if (os.path.exists(targetpath) and parameters['OVERWRITE']) or not os.path.exists(targetpath):
            if os.path.exists(targetpath):
                os.remove(targetpath)
            
            searchStr = '"routeID" = \'' + opid + '\''
            templatePath = os.path.join(templatesPath, '000_Targets.gpkg')
            layer = QgsProject.instance().mapLayersByName('000_TargetsBriefing')[0]
            style = os.path.join(stylespath, 'Targets.qml')
            
            # select features of 000_TargetsBriefing
            layer.selectByExpression(searchStr, QgsVectorLayer.SetSelection)
            # make a copy of 000_TargetsBriefing, only the seleted features
            newLayer = layer.materialize(QgsFeatureRequest().setFilterFids(layer.selectedFeatureIds()))

            # CLEANUP FIELDS
            if debug: print('CLEANUP FIELDS')
            fieldsNeeded = [
                'address',
                'phone',
                'customer',
                'deliveries',
                'name',
                'contact',
                'email',
                '0',
                'notes'
                ]
            newLayer.startEditing()
            if debug: print(f"Checking fields")
            for field in newLayer.fields():
                # print(f"Checking field: {field.name()}")
                if field.name().lower() not in fieldsNeeded:
                    newLayer.deleteAttribute(findField(newLayer,field.name()))
                    if debug: print(f"Deleted field: {field.name()}")
            newLayer.addAttribute(QgsField('descr', QVariant.String, len=100))
            newLayer.addAttribute(QgsField('id', QVariant.Int, len=10))
            # newLayer.updateFields()
            newLayer.commitChanges()
            
           
            # descr default value
            if debug: print(f"descr default value")
            newLayer.startEditing()
            for feature in newLayer.getFeatures():
                newLayer.changeAttributeValue(feature.id(),findField(newLayer, 'descr'),'Target')
            newLayer.commitChanges()
            
            # reproject
            if debug: print(f"Reprojecting: {newLayer}")
            parameter = {
                'INPUT': newLayer,
                'TARGET_CRS': 'EPSG:3857',
                'OUTPUT': 'memory:' + newLayer.name()
            }
            result = processing.run('native:reprojectlayer', parameter)['OUTPUT']
            newLayer = QgsProject.instance().addMapLayer(result)
            if debug: print(f"Reprojected: {newLayer}")
            
            
            if debug: print(f"Exporting {newLayer}")
            if debug: print(f"Layer name {newLayer.name()}")
            layer = common.export_layer(
                input_layer=newLayer,
                opnum=opnum,
                newlayer_type='Targets',
                overwrite=parameters['OVERWRITE'],
                driverName="GPKG",
                crs=QgsCoordinateReferenceSystem('EPSG:3857'),
                add_layer=True,
                apply_style=True,
                destpath=oppath,
                debug=debug
                )
            if debug: print(f"Exported layer: {layer}")
            layers.append(layer)

            # clean temproary layer
            if debug: print(f"Removing temporary layer {newLayer}")
            QgsProject.instance().removeMapLayer(newLayer.id())
            
            # make clipper from targets
            if debug: print(f"\nCLIPPER")
            newlayername =  opnum + '_Clipper'
            targetpath = os.path.join(oppath, newlayername + '.gpkg')
            style = os.path.join(stylespath, 'Clipper.qml')
            result = processing.run("native:buffer",
                {
                'INPUT': layer,
                'DISTANCE': 5000,
                'OUTPUT':targetpath,
                'DISSOLVE':True
                })
            layer = iface.addVectorLayer(targetpath, newlayername,'ogr')
            QgsProject.instance().addMapLayer(layer, False)
            layer.loadNamedStyle(style) # layer styling
            layer.triggerRepaint() # layer styling repaint

        if debug:
            print('\nLayers')
            for layer in layers: print(layer)
            
        # FLIGHT PATH DRAFT
        if debug: print(f"\nFLIGHT PATH DRAFT")
        # search the flight path and targets
        for layer in layers:
            if layer.name().split('_')[1] == 'fp':
                layerFp = layer
                if debug: print(f"layerFP found")
            elif layer.name().split('_')[1] == 'Targets':
                layerTargets = layer
                if debug: print(f"layerTargets found")

        if layerFp.isValid() and layerTargets.isValid():
            
            # Group points from layerTargets by the field '0'
            point_groups = defaultdict(list)
            for feature in layerTargets.getFeatures():
                group_key = feature['0']  # Field used to group points
                point_xy = feature.geometry().asPoint()
                # Convert QgsPointXY to QgsPoint
                point = QgsPoint(point_xy.x(), point_xy.y())
                point_groups[group_key].append(point)
           
            # Create lines for each pair of points and add to layerFp
            fp_provider = layerFp.dataProvider()
            for points in point_groups.values():
                if len(points) == 2:  # Ensure there are exactly 2 points
                    line = QgsGeometry.fromPolyline(points)
                    line_feature = QgsFeature()
                    line_feature.setGeometry(line)
                    fp_provider.addFeature(line_feature)

            # Update the layerFp layer
            layerFp.updateExtents()
            layerFp.triggerRepaint()
            
            # default attributes
            layerFp.startEditing()
            for counter, feature in enumerate(layerFp.getFeatures()):
                layerFp.changeAttributeValue(feature.id(),findField(layerFp, 'id'),counter)
                layerFp.changeAttributeValue(feature.id(),findField(layerFp, 'altitude'),'105')
                layerFp.changeAttributeValue(feature.id(),findField(layerFp, 'fmode'),'Cruising')
                layerFp.changeAttributeValue(feature.id(),findField(layerFp, 'speed(m/s)'),'30')
                layerFp.changeAttributeValue(feature.id(),findField(layerFp, 'fg'),'70')
                layerFp.changeAttributeValue(feature.id(),findField(layerFp, 'cv'),'35')
                layerFp.changeAttributeValue(feature.id(),findField(layerFp, 'grb'),'120')
                layerFp.changeAttributeValue(feature.id(),findField(layerFp, 'status'),'Active')
            layerFp.commitChanges()
            
            # reset ratio
            #layer.setCustomProperty('ratio', '1')
            
            # Calculate and apply ratio
            ratio = common.get_ratio(layerFp, debug=debug, apply=True)
            
            # Apply ratio to other layers
            layer_types_with_ratio = [
                'FlightArea',
                'AirRisk'
                ]
            if debug: print("Applying Ratio {ratio} to other layers")
            for layer in layers:
                if layer.name().split('_')[1] in layer_types_with_ratio:
                    QgsExpressionContextUtils.setLayerVariable(layer, 'ratio', ratio)
                    if debug: print(f"{layer} Ratio stored: {ratio}")
                else:
                    if debug: print(f"{layer} Ratio skipped")
            
            
            feedback.pushInfo(f"Flight path draft COMPLETED")
            if debug: print(f"Flight path draft COMPLETED")
        else:
            raise QgsProcessingException('ERROR: flight path or targets layers are missing')
        


        # SEGMENTS
        if debug: print(f"\nSEGMENTS")

        # Search the Segments layer
        for layer in layers:
            if layer.name().split('_')[1] == 'Segments':
                layerSegments = layer
                if debug: print(f"layerSegments found: {layerSegments}")

        if layerTargets.isValid() and layerSegments.isValid():
            
            if debug: print(f"Processing {layerTargets.featureCount()} target features...")

            # Group points from layerTargets by the field '0'
            point_groups = defaultdict(list)
            for feature in layerTargets.getFeatures():
                group_key = feature['0']  # Field used to group points
                point_xy = feature.geometry().asPoint()
                point = QgsPoint(point_xy.x(), point_xy.y())  # Convert QgsPointXY to QgsPoint
                point_groups[group_key].append((point, feature['deliveries'], feature['notes']))

            if debug: print(f"Grouped targets into {len(point_groups)} groups.")

            # Create lines for each pair of points and add to layerSegments
            segments_provider = layerSegments.dataProvider()
            new_features = []

            for group_key, points in point_groups.items():
                if debug: print(f"Processing group {group_key} with {len(points)} points.")
                if len(points) == 2:  # Ensure there are exactly 2 points
                    point1, deliveries1, notes1 = points[0]
                    point2, deliveries2, notes2 = points[1]

                    line = QgsGeometry.fromPolyline([point1, point2])
                    if not line.isEmpty():
                        line_feature = QgsFeature(layerSegments.fields())  # Ensure feature has correct attributes
                        line_feature.setGeometry(line)

                        # Ensure deliveries_per_week is taken from one of the targets
                        # deliveries_per_week = deliveries1 if deliveries1 == deliveries2 else None
                        # if debug: print(f"Assigned deliveries_per_week: {deliveries_per_week}")
                        
                        if debug: print(f"Assigned deliveries_per_week: {deliveries1}")
                        if debug: print(f"Assigned notes: {notes1}")                        

                        # Set attributes
                        line_feature.setAttribute(findField(layerSegments, 'status'), 'Active')
                        #line_feature.setAttribute(findField(layerSegments, 'deliveries_per_week'), deliveries_per_week)
                        line_feature.setAttribute(findField(layerSegments, 'deliveries_per_week'), deliveries1)
                        line_feature.setAttribute(findField(layerSegments, 'notes'), notes1)
                        line_feature.setAttribute(findField(layerSegments, 'group'), 'TEMP')

                        new_features.append(line_feature)
                    else:
                        if debug: print(f"Warning: Empty geometry for group {group_key}.")

            if new_features:
                segments_provider.addFeatures(new_features)
                layerSegments.updateExtents()
                layerSegments.triggerRepaint()
                if debug: print(f"Added {len(new_features)} features to layerSegments.")
            else:
                if debug: print("No segments were created.")

            # Assign unique IDs to segment features
            if debug: print("Updating segment attributes...")

            layerSegments.startEditing()
            for counter, feature in enumerate(layerSegments.getFeatures()):
                line_geom = feature.geometry()
                if not line_geom or line_geom.isEmpty():
                    if debug: print(f"Skipping empty feature {feature.id()}")
                    continue

                # Ensure we have only LineString (convert from MultiLineString if necessary)
                if line_geom.type() == QgsWkbTypes.MultiLineString:
                    line_geom = line_geom.geometryN(0)  # Extract first LineString
                    feature.setGeometry(line_geom)  # Update geometry in the feature

                if line_geom.type() != QgsWkbTypes.LineString:
                    if debug: print(f"Skipping feature {feature.id()} due to invalid geometry type: {line_geom.type()}")
                    continue

                # Assign attributes
                layerSegments.changeAttributeValue(feature.id(), findField(layerSegments, 'id'), counter)
                layerSegments.changeAttributeValue(feature.id(), findField(layerSegments, 'status'), 'Active')

            layerSegments.commitChanges()

            feedback.pushInfo(f"Segments layer COMPLETED")
            if debug: print(f"Segments layer COMPLETED with {layerSegments.featureCount()} features.")

        else:
            raise QgsProcessingException('ERROR: segments or targets layers are invalid')






        # CLEAN TARGETS LAYER (remove duplicated points)
        if debug: print(f"\nCLEAN TARGETS LAYER")

        unique_geometries = set()
        features_to_keep = []

        layerTargets.startEditing()

        for feature in layerTargets.getFeatures():
            geom = feature.geometry().asWkt()  # Convert geometry to WKT string for easy comparison
            if geom not in unique_geometries:
                unique_geometries.add(geom)
                features_to_keep.append(feature)

        # Remove all features first
        layerTargets.deleteFeatures([f.id() for f in layerTargets.getFeatures()])

        # Reinsert only the unique features
        for counter, feature in enumerate(features_to_keep):
            feature.setAttribute(findField(layerTargets, 'id'), counter)  # Assign unique ID
            layerTargets.addFeature(feature)

        layerTargets.commitChanges()

        if debug: print(f"Cleaned Targets layer, removed duplicates and assigned unique IDs")


        # TARGETS IDs TO SEGMENTS
        if debug: print(f"\nTARGETS IDs TO SEGMENTS")

        if layerTargets.isValid() and layerSegments.isValid():
            
            if debug: print(f"Processing {layerSegments.featureCount()} segment features...")

            # Create a spatial index for fast lookups of target points
            target_index = QgsSpatialIndex(layerTargets)
            
            # Create a dictionary to store target IDs by geometry
            target_id_map = {target.geometry().asPoint(): target['id'] for target in layerTargets.getFeatures()}

            layerSegments.startEditing()
            for counter, feature in enumerate(layerSegments.getFeatures()):
                line_geom = feature.geometry()
                if not line_geom or line_geom.isEmpty():
                    if debug: print(f"Skipping empty segment feature {feature.id()} due to missing geometry")
                    continue

                # Ensure we have a single LineString (convert if MultiLineString)
                if line_geom.wkbType() == QgsWkbTypes.MultiLineString:
                    multi_geom = line_geom.asGeometryCollection()
                    if multi_geom:
                        line_geom = multi_geom[0]  # Extract first LineString
                        feature.setGeometry(line_geom)  # Update geometry in the feature

                if line_geom.wkbType() != QgsWkbTypes.LineString:
                    if debug: print(f"Skipping feature {feature.id()} due to invalid geometry type: {line_geom.wkbType()}")
                    continue

                # Get start and end points of the segment
                start_point = line_geom.asPolyline()[0]
                end_point = line_geom.asPolyline()[-1]

                # Find nearest target IDs using spatial index
                start_candidates = target_index.nearestNeighbor(start_point, 1)
                end_candidates = target_index.nearestNeighbor(end_point, 1)

                a_id = target_id_map.get(layerTargets.getFeature(start_candidates[0]).geometry().asPoint()) if start_candidates else NULL
                b_id = target_id_map.get(layerTargets.getFeature(end_candidates[0]).geometry().asPoint()) if end_candidates else NULL

                # Assign attributes
                segment_id = counter  # Unique ID for this segment
                layerSegments.changeAttributeValue(feature.id(), findField(layerSegments, 'id'), segment_id)
                layerSegments.changeAttributeValue(feature.id(), findField(layerSegments, 'a'), a_id)
                layerSegments.changeAttributeValue(feature.id(), findField(layerSegments, 'b'), b_id)

                if debug: print(f"Segment {feature.id()} → ID: {segment_id}, a: {a_id}, b: {b_id}")

            layerSegments.commitChanges()

            feedback.pushInfo(f"Target IDs and Unique IDs assigned to Segments")
            if debug: print(f"Target IDs and Unique IDs assigned to Segments.")

        else:
            raise QgsProcessingException('ERROR: Segments or Targets layers are missing or invalid')





        return {}