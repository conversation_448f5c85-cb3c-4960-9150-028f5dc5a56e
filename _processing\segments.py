from qgis.PyQt.QtCore import QCoreApplication
from qgis.core import (QgsProcessing,
                       QgsFeatureSink,
                       QgsProcessingException,
                       QgsProcessingFeedback,
                       QgsProcessingAlgorithm,
                       QgsProcessingParameterFeatureSource,
                       QgsProcessingParameterFeatureSink,
                       QgsProcessingParameterNumber,
                       QgsProcessingParameterString,
                       QgsProcessingParameterBoolean,
                       QgsProcessingParameterFolderDestination,
                       QgsProcessingParameterRasterLayer,
                       QgsProcessingParameterEnum,
                       QgsCoordinateReferenceSystem,
                       QgsRasterLayer,
                       QgsDataSourceUri,
                       QgsProject,
                       QgsDistanceArea,
                       QgsWkbTypes,
                       QgsField,
                       QgsVectorFileWriter,
                       QgsProperty,
                       QgsVectorLayer,
                       QgsApplication, 
                       QgsRectangle,
                       QgsCoordinateTransform,
                       QgsGeometry,
                       QgsFeature,
                       QgsFields,
                       QgsLayerTreeLayer,
                       QgsFeatureRequest,
                       QgsRasterBandStats,
                       NULL)


import os
import time
import gc  # Garbage collection module
from qgis import processing
from qgis import os
from qgis.PyQt.QtWidgets import QAction, QMessageBox, QFileDialog
from qgis.utils import iface
from qgis.PyQt.QtCore import QVariant
from osgeo import ogr

# IMPORT COMMON SCRIPTS
import importlib.util
import sys
module_path = os.path.join(os.path.dirname(QgsProject.instance().fileName()), '_processing','common.py')
spec = importlib.util.spec_from_file_location("common", module_path)
common = importlib.util.module_from_spec(spec)
sys.modules["common"] = common
spec.loader.exec_module(common)

class segments(QgsProcessingAlgorithm):

    FP = 'FP'
    DEBUG = 'DEBUG'
    
    def tr(self, string):
        return QCoreApplication.translate('Processing', string)

    def createInstance(self):
        return segments()

    def name(self):
        return 'segments'

    def displayName(self):
        return self.tr('Segments')

    def group(self):
        return self.tr('Jedsy scripts')

    def groupId(self):
        return 'jedsyscripts'

    message = (
        "Select the desired flight path sections of the fp layer\n"
        "Select the target segment\n"
        "The script will store the connection field of the segment layer with the selected fp sections\n"
        "v00"
    )

    def shortHelpString(self):
        return self.tr(self.message)

    def initAlgorithm(self, config=None):
        self.addParameter(
            QgsProcessingParameterFeatureSource(
                self.FP,
                self.tr('Input FLIGHT PATH vector layer'),
                [QgsProcessing.TypeVectorAnyGeometry]
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                name=self.DEBUG,
                description='Debug mode',
                defaultValue=False
            )
        )

    def processAlgorithm(self, parameters, context, feedback):

        # CONFIGURATION
        debug = self.parameterAsBoolean(parameters, self.DEBUG, context)
        layer_fp = self.parameterAsVectorLayer(parameters, self.FP, context)

        # Validate input flight path layer
        if layer_fp is None:
            raise QgsProcessingException("ERROR: Flight Path layer is not valid or not provided.")

        # Determine corresponding segments layer name
        opNum = layer_fp.name().split('_')[0]
        layer_segments = common.find_layer_by_name(opNum + '_Segments')

        # Validate segments layer
        if not layer_segments:
            raise QgsProcessingException(f"ERROR: Segments layer '{opNum}_Segments' not found.")

        

        # Ensure at least one feature is selected in layer_fp
        selected_fp_features = layer_fp.selectedFeatures()
        if not selected_fp_features:
            raise QgsProcessingException("ERROR: No flight path features selected. Please select at least one.")
        
        if debug: print(f"selected_fp_features {selected_fp_features}")
        
        # Generate the connection string (IDs of selected features)
        # connection_string = " ".join(str(f.id()) for f in selected_fp_features)
        connection_string = " ".join(str(f["id"]) for f in selected_fp_features)
        if debug: print(f"connection_string {connection_string}")

        # Ensure exactly one feature is selected in layer_segments
        selected_segments_features = layer_segments.selectedFeatures()
        if len(selected_segments_features) != 1:
            raise QgsProcessingException("ERROR: Exactly one segment feature must be selected.")

        segment_feature = selected_segments_features[0]

        # Identify the connection field in the segments layer
        connection_field_index = common.find_field(layer_segments, "connection")
        if connection_field_index == -1:
            raise QgsProcessingException("ERROR: 'connection' field not found in the segments layer.")

        # Start an edit session to update the connection field
        layer_segments.startEditing()
        segment_feature[connection_field_index] = connection_string
        layer_segments.updateFeature(segment_feature)
        layer_segments.commitChanges()

        feedback.pushInfo(f"Updated segment ID {segment_feature.id()} with connection string: {connection_string}")
        if debug: print(f"Updated segment ID {segment_feature.id()} with connection string: {connection_string}")

        return {"Updated Segment": segment_feature.id(), "Connection String": connection_string}
