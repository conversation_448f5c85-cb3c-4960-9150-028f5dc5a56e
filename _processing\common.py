from qgis.PyQt.QtCore import(
    QCoreApplication,
    QVariant)
from qgis.core import (QgsProcessing,
                       QgsFeatureSink,
                       QgsProcessingException,
                       QgsProcessingAlgorithm,
                       QgsProcessingParameterMapLayer,
                       QgsProcessingParameterFeatureSource,
                       QgsProcessingParameterFeatureSink,
                       QgsProcessingParameterNumber,
                       QgsProcessingParameterString,
                       QgsProcessingParameterBoolean,
                       QgsProcessingParameterFolderDestination,
                       QgsProcessingParameterMultipleLayers,
                       QgsProcessingParameterEnum,
                       QgsProject,
                       QgsVectorFileWriter,
                       QgsCoordinateTransformContext,
                       QgsCoordinateReferenceSystem,
                       QgsVectorLayer,
                       QgsVectorDataProvider,
                       QgsFeatureRequest,
                       QgsField,
                       QgsVectorLayerUtils,
                       QgsExpressionContext,
                       QgsExpressionContextUtils,
                       QgsCoordinateTransform,
                       QgsGeometry,
                       QgsWkbTypes,
                       QgsCategorizedSymbolRenderer,
                       QgsSymbol,
                       QgsRendererCategory,
                       QgsMapLayer,
                       NULL)
from qgis import processing
from qgis import os
from qgis.PyQt.QtWidgets import QAction, QMessageBox, QFileDialog
from qgis.utils import iface
import xml.etree.ElementTree as ET
from qgis.PyQt.QtCore import NULL  # For handling NULL values
import gc  # Garbage collector
import math
from qgis.gui import QgsLayerTreeMapCanvasBridge
import glob
import re


# GENERAL VARIABLES
project = QgsProject.instance()
projectpath = os.path.dirname(project.fileName())
stylespath = projectpath
templates_path = os.path.join(projectpath, '000_Templates')



########################
######## LAYERS ########
########################

def get_standardized_path(layer):
    # Function to get a standardized, absolute path from a layer
    if layer.type() == layer.VectorLayer:
        # For vector layers, use dataProvider().dataSourceUri()
        path = layer.dataProvider().dataSourceUri().split('|')[0]  # Remove any additional parameters
    else:
        # For raster layers, use source()
        path = layer.source()
    
    # Convert to absolute path (if not already) and standardize the path format
    return os.path.abspath(os.path.normpath(path))
   
def export_layer(input_layer, opnum='', newlayer_type=None, overwrite=True, driverName="GPKG", crs=None, add_layer=True, apply_style=True, destpath=None, debug=False):
    if destpath is None:
        destpath = os.path.dirname(input_layer.dataProvider().dataSourceUri())
    
    # Set output format
    if driverName == 'GPKG':
        extension = '.gpkg'
        deleteFields = True
    elif driverName == 'ESRI shapefile':
        extension = '.shp'
        deleteFields = True
    elif driverName == 'KML':
        extension = '.kml'
        deleteFields = False    
    else:
        raise ValueError(f"ERROR: driver name unknown: {driverName}")
    
    # configure names and paths
    source_name = input_layer.name()
    source_opnum = source_name.split('_')[0]
    source_type = source_name.split('_',1)[-1]

    ratio_value = manage_layer_variable(input_layer,variable='ratio',write=False,debug=debug)
    '''
    # Store ratio
    context = QgsExpressionContext()
    context.appendScopes(QgsExpressionContextUtils.globalProjectLayerScopes(input_layer))
    ratio_value = context.variable('ratio')
    if debug: print(f"ratio stored: {ratio_value}")    
    '''
    
    
    # Opnum
    target_opnum = opnum if opnum else source_opnum
    if debug: print(f"opnum {opnum}")
    
    # Target type
    target_type = newlayer_type if newlayer_type else source_type


    #target_type = source_type if newlayer_type is None else newlayer_type
    if debug: print(f"target_type {target_type}")
    targetname = target_opnum + '_' + target_type
    if debug: print(f"targetname {targetname}")
    targetpath = os.path.abspath(os.path.normpath(os.path.join(destpath, targetname + extension)))
    if debug: print(f"targetpath {targetpath}")

    
    # Check if a layer with the same name already exists and attempt to remove it (doesn't always work)
    layer_same_name = project.mapLayersByName(targetname)
    for layer in layer_same_name:
        if debug: print(f"checking: {layer}")
        if debug: print(f"checking: {get_standardized_path(layer)}")
        if debug: print(f"checking: {targetpath}")
        if get_standardized_path(layer) == targetpath and overwrite:
            if debug: print(f"Target file exists: {targetpath}\nRemoving")
            project.removeMapLayer(layer.id())
            del layer
        elif get_standardized_path(layer) == targetpath and not overwrite:
            errMsg = f"ERROR: The a layer named {targetname} already exists and overwriting is disabled. Enable overwriting and try again."
            return errMsg
    gc.collect()
    
    # Export Layer
    if (os.path.exists(targetpath) and overwrite) or not os.path.exists(targetpath):
        if os.path.exists(targetpath):
            os.remove(targetpath)
            if debug: print(f"removed: {targetpath}")

        # Save layer options
        options = QgsVectorFileWriter.SaveVectorOptions()
        options.driverName = driverName
        options.fileEncoding = "UTF-8"
        options.layerName = targetname  # Use the target name as the layer name within the GeoPackage
        options.destCRS = crs if crs else input_layer.crs()

        output = QgsVectorFileWriter.writeAsVectorFormatV3(input_layer, targetpath, QgsCoordinateTransformContext(), options)
        if output[0] != QgsVectorFileWriter.NoError:
            errMsg = f"ERROR: Failed to save {input_layer.name()}: {output[1]}"
            if debug: print(errMsg)
            return
        else:
            if debug: print(f"Write successfull: {output}")

        if add_layer:
            layer_path = f"{targetpath}|layername={targetname}"
            layer = QgsVectorLayer(layer_path, targetname, "ogr")
            if layer.isValid():
                QgsProject.instance().addMapLayer(layer)
                print(f"Layer {targetname} loaded successfully.")
                
                # Restore ratio
                if ratio_value != NULL and layer.isValid():
                    manage_layer_variable(layer,variable='ratio',write=True,debug=debug,ratio_value=ratio_value)
                    '''
                    QgsExpressionContextUtils.setLayerVariable(layer, 'ratio', ratio_value)
                    if debug: print(f"Ratio restored: {ratio_value}")
                    '''
                # style layer
                if apply_style and layer.isValid():
                    style_layer(layer, debug=debug)                

            else:
                print(f"Failed to load layer from {layer_path}")

        if debug: print(f"{input_layer.name()} Saved as: {targetpath}")
        return layer
    else:
        errMsg = f"ERROR: The layer already exists and overwriting is disabled. Enable overwriting and try again. {targetpath}"
        print(errMsg)
        return errMsg
    
def assign_default_values(layer, field_names, debug=False):
    """ Assigns default values to specified fields based on layer's default value definitions """
    layer.startEditing()
    for field_name in field_names:
        field_idx = layer.fields().indexFromName(field_name)
        if field_idx == -1:
            if debug:
                print(f"Field {field_name} not found in layer.")
            continue
        
        # Get the default value definition for the field at the given index
        default_def = layer.defaultValueDefinition(field_idx)
        
        # Check if there is a valid default definition and it has a non-empty expression
        if default_def and default_def.expression():
            # Evaluate the expression safely
            default_value = eval(default_def.expression(), {}, {"value": NULL})  # Safely evaluate the expression
            # Update empty attributes with the evaluated default value
            for feature in layer.getFeatures():
                if feature[field_idx] in (NULL, '', None):  # Check for uninitialized fields
                    layer.changeAttributeValue(feature.id(), field_idx, default_value)
                    if debug: 
                        print(f"Default value for {field_name} set to {default_value}")
    # Commit changes to the layer
    layer.commitChanges()
    if debug: 
        print("Default values assigned to new fields.")


def find_layer_by_name(layer_name):
    layers = QgsProject.instance().mapLayersByName(layer_name)
    
    if not layers:
        return None  # Return None if the layer is not found
    
    if len(layers) > 1:
        print(f"WARNING: Multiple layers found with name '{layer_name}', returning the first one.", "QGIS", Qgis.Warning)
    
    return layers[0]  # Return the first matching layer

# get opnum and fp layer from any layer
def find_op(layer: QgsMapLayer) -> tuple[str, QgsMapLayer]:
    if not isinstance(layer, QgsMapLayer):
        raise TypeError("Expected a QgsMapLayer object.")

    name = layer.name()
    if not name:
        raise ValueError("Layer has no name.")

    match = re.match(r'^(\d{3})_', name)
    if not match:
        raise ValueError(f"Layer name does not start with a 3-digit opnum: '{name}'")

    opnum = match.group(1)
    layer_fp = find_layer_by_name(f"{opnum}_fp")

    return opnum, layer_fp

def toggle_layer_visibility(layer, show=True, debug=False):
    if debug: print(f"########## toggle_layer_visibility: layer {layer} show {show}")
    # Get the root of the layer tree
    root = QgsProject.instance().layerTreeRoot()
    # Find the corresponding layer tree layer
    layer_tree_layer = root.findLayer(layer.id())
    if not layer_tree_layer:
        if debug: print(f"ERROR: Layer tree layer for {layer} not found")
        return

    # Show or hide the layer based on the 'show' parameter
    if show:
        layer_tree_layer.setItemVisibilityChecked(True)
        if debug: print(f"{layer} is now visible")
    else:
        layer_tree_layer.setItemVisibilityChecked(False)
        if debug: print(f"{layer} is now hidden")

    # Refresh the layer tree view to update the visibility
    iface.layerTreeView().refreshLayerSymbology(layer.id())




########################
###### RATIO ########
########################
def manage_layer_variable(layer,variable='ratio',write=False,debug=False,ratio_value=1):
    # MANAGE RATIO (read and store from layer or write it to layer)
    if debug: print(f"########## common manage_layer_variable")
    if write == False:
        context = QgsExpressionContext()
        context.appendScopes(QgsExpressionContextUtils.globalProjectLayerScopes(layer))
        ratio_value = context.variable(variable)
        if debug: print(f"ratio stored: {ratio_value}")
        return ratio_value
    else:
        QgsExpressionContextUtils.setLayerVariable(layer, variable, ratio_value)
        if debug: print(f"Ratio restored on {layer}: {ratio_value}")     

def calculate_scale_factor(latitude):
    """Calculate scale factor based on latitude."""
    return 1 / math.cos(math.radians(latitude))

def get_ratio(layer, debug=False, apply=True):
    if debug: print(f"########## common get_ratio: calculate the ratio")
    # Check if the layer is valid
    if not layer:
        print("Error: Source layer not found.")
        return -1

    # Check if the layer is a flight path
    opnum = layer.name().split('_')[0]
    layer_type = layer.name().split('_')[1]
    is_flight_path = True if opnum and layer_type == 'fp' else False

    # Set the ratio source layer
    if is_flight_path:
        layer_source = layer
        if debug: print(f"{layer} is a flight path: {is_flight_path}\n{opnum} {layer_type}")        
    else:
        # Search for the flight path
        if debug: print(f"{layer} is NOT a flight path: searching for fp layer")
        opnum = layer.name().split('_')[0]
        layer_type = layer.name().split('_')[1]
        if opnum and layer_type:
            layer_source_name = opnum + '_' + 'fp'
            search_result = find_layer_by_name(layer_source_name)
            if len(search_result) == 1:
                layer_source = search_result[0]
                if debug: print(f"Fp layer found: {layer_source}")
            elif len(search_result) == 0:
                print(f"ERROR: could not find {layer_source_name}")
                return -1
            else:
                print(f"ERROR: found multiple instances of layers named {layer_source_name}:")
                for layer in search_result:
                    print(layer)
                return -1

    # Check if the source layer is a line or multiline layer
    if layer_source.geometryType() != QgsWkbTypes.LineGeometry:
        print(f"{layer_source} is not a line or multiline layer.")
        return -1

    # Check if the layer has at least one feature
    if layer_source.featureCount() == 0:
        print("Error: The layer_source has no features.")
        return -1

    # Setup coordinate transformation from the layer's CRS to EPSG:4326
    transform = QgsCoordinateTransform(
        layer_source.crs(),
        QgsCoordinateReferenceSystem('EPSG:4326'),
        QgsProject.instance()
    )

    # Combine all geometries to find the overall centroid
    combined_geom = QgsGeometry()
    for feature in layer_source.getFeatures():
        feature_geom = feature.geometry()
        if not feature_geom:
            continue  # Skip features without geometry
        if combined_geom.isEmpty():
            combined_geom = QgsGeometry(feature_geom)
        else:
            combined_geom = combined_geom.combine(feature_geom)

    if combined_geom.isEmpty():
        print("Error: No valid geometries to combine.")
        return -1

    # Get the centroid of the combined geometry
    centroid = combined_geom.centroid()
    centroid.transform(transform)
    latitude = centroid.asPoint().y()

    # Calculate the scale factor based on the centroid's latitude
    ratio = calculate_scale_factor(latitude)
    if debug: print(f"Calculated ratio: {ratio}")
    
    # Apply the ratio
    if apply:
        QgsExpressionContextUtils.setLayerVariable(layer, 'ratio', ratio)
        if debug: print(f"{layer} Ratio stored: {ratio}")    

    return ratio


########################
######## STYLE #########
########################
def style_layer(layer, deleteFields=True, add_fields=True, debug=False):
    if debug: print(f"########## common style_layer")
    
    # Store ratio
    # ratio_value = manage_layer_variable(layer,variable='ratio',write=False,debug=debug,ratio_value=1)
    
    '''
    # Store ratio from fp layer if possible
    layer_name_split = layer.name().split('_')
    opnum = layer_name_split[1]
    
    if opnum:
        layer_source = find layer by name: opnum + '_fp'
    else:
        layer_source = layer

    ratio_value = manage_layer_variable(layer_source,variable='ratio',write=False,debug=debug,ratio_value=1)
    '''

    # Attempt to parse the opnum from the layer name
    layer_name_parts = layer.name().split('_')

    opnum = None
    if len(layer_name_parts) > 1:
        opnum = layer_name_parts[0]
    #if debug: print(f"opnum {opnum}")

    # Try to find the corresponding '_fp' layer if opnum is valid
    layer_source = None
    if opnum:
        fp_layer_name = f"{opnum}_fp"
        matching_layers = [l for l in QgsProject.instance().mapLayers().values() if l.name() == fp_layer_name]
        if matching_layers:
            layer_source = matching_layers[0]

    # Fallback to input layer if matching fp layer is not found
    if not layer_source:
        layer_source = layer
    #if debug: print(f"layer_source {layer_source}")
    
    # Retrieve 'ratio' variable using the manage_layer_variable function
    try:
        ratio_value = manage_layer_variable(layer_source, variable='ratio', write=False, debug=debug, ratio_value=1)
    except Exception as e:
        if debug:
            QgsMessageLog.logMessage(f"Error retrieving 'ratio': {e}", 'PyQGIS', Qgis.Warning)
            if debug: print(f"Error retrieving 'ratio': {e}", 'PyQGIS', Qgis.Warning)
        ratio_value = 1
    if debug: print(f"ratio from fp {ratio_value}")
    
    # Find Style
    layerNameSplit = layer.name().split('_') # use lower, but change also the styles files
    if debug: print(f"layerNameSplit: {layerNameSplit}")
    if layerNameSplit[1] == 'population':
        style = os.path.join(stylespath, layerNameSplit[1] + '_' + layerNameSplit[2]+ '.qml')
    elif layerNameSplit[len(layerNameSplit)-1] == 'pos':
        style = os.path.join(stylespath, 'pos.qml')
    else:
        style = os.path.join(stylespath, layerNameSplit[1] + '.qml')
    if debug: print(f"Style: {style}")

    # CHECK FIELDS
    virtual_field_names, normal_field_names = get_virtual_fields_with_expressions(style, debug=debug)
    
    # DELETE DUPLICATE FIELDS
    if deleteFields:
        if debug: print('DELETE DUPLICATE FIELDS')
        remove_fields(virtual_field_names, layer=layer, debug=debug)

    # LOAD STYLE must be after deleting duplicate fields or it will delete the virtual fiedls
    layer.loadNamedStyle(style)
    
    # ADD MISSING FIELDS
    if add_fields:
        if debug: print('ADD MISSING FIELDS')
        add_missing_fields(normal_field_names, layer=layer, debug=debug)
        assign_default_values(layer, normal_field_names, debug=debug)
    
    # Restore ratio
    if ratio_value != NULL and layer.isValid():
        manage_layer_variable(layer,variable='ratio',write=True,debug=debug,ratio_value=ratio_value)


    if layerNameSplit[1] == 'GroundRisk':
        if debug: print("\nUpdate rally points names")

        field_names = [f.name() for f in layer.fields()]
        required_fields = ['descr', 'rallyname', 'name']
        missing_fields = [f for f in required_fields if f not in field_names]

        if missing_fields:
            if debug: print(f"ERROR Missing required fields for GroundRisk logic: {missing_fields}")
        else:
            if not layer.isEditable():
                layer.startEditing()

            for feat in layer.getFeatures():
                if feat['descr'] == 'Rally':
                    new_name = feat['rallyname']
                    if new_name is not None:
                        idx = layer.fields().indexOf('name')
                        layer.changeAttributeValue(feat.id(), idx, new_name)
                        if debug: print(f"Feature ID {feat.id()}: updated 'name' to '{new_name}'")

        # Commit and end editing session
        if not layer.commitChanges():
            if debug: print("ERROR: Failed to commit changes to layer")
        else:
            if debug: print("Changes committed successfully")

    layer.triggerRepaint()

def save_layer_style(layer,debug=False):
    # Open a file dialog to specify the filename and location for the QML file
    # file_name, _ = QFileDialog.getSaveFileName(None, "Save Layer Style", "", "QGIS Layer Style Files (*.qml)")
    
    layerNameSplit = layer.name().split('_') # use lower, but change also the styles files
    file_name = os.path.join(stylespath, layerNameSplit[1] + '.qml')

    # Save the style to the specified file
    success, message = layer.saveNamedStyle(file_name)
    
    if debug:
        if success: print(f"Saved {layerNameSplit[1]} style to {file_name}")
        else: print(f"ERROR, could not save style {layerNameSplit[1]} to {file_name}")


###### FIELDS ########
def get_virtual_fields_with_expressions(qml_path, debug=False):
    try:
        # Load and parse the QML file
        tree = ET.parse(qml_path)
        root = tree.getroot()

        # Find elements with expressions
        potential_virtual_fields = root.findall(".//*[@expression]")
        
        # Initialize lists
        all_field_names = []
        virtual_fields_names = []
        normal_field_names = []

        # Iterate over potential virtual fields and filter by non-empty expressions
        for field in potential_virtual_fields:
            expression = field.attrib.get('expression').strip()
            # if debug: print(f"field name {field.attrib.get('name')} - field {field.attrib.get('field')} - expression: {expression}")
            
            if field.attrib.get('field'):
                all_field_names.append(field.attrib.get('field'))
            
            if field.attrib.get('name'):
                virtual_fields_names.append(field.attrib.get('name')) 

        normal_field_names = [name for name in all_field_names if name not in virtual_fields_names]
        
        if debug:
            print(f"Virtual fields: {virtual_fields_names}")
            print(f"normal_field_names: {normal_field_names}")

        return virtual_fields_names, normal_field_names
    except Exception as e:
        print(f"An error occurred: {e}")
        return []

def add_missing_fields(field_names, layer, debug=False):
    if debug: print(f"Adding fields: {field_names}")
    layer_type = layer.name().split('_')[1]

    # List all GeoPackage files in the directory
    template_files = [file for file in os.listdir(templates_path) if file.endswith('.gpkg')]

    # Find the correct template based on layer type
    template_layer = None
    for file in template_files:
        # Assuming the file naming convention is similar to the layer naming
        # if debug: print(f"templates: {file} - comparing {file.split('_')[1].split('.')[0]} to {layer_type}")
        if file.split('_')[1].split('.')[0] == layer_type:
            template_path = os.path.join(templates_path, file)
            # Load the GeoPackage layer
            template_layer = QgsVectorLayer(template_path, "template_layer", "ogr")
            if template_layer.isValid():
                if debug: print(f"template found: {template_layer}")
                break
            else:
                print(f"ERROR: template not valid")
        #else:
            #print(f"WARNING: file split: {file.split('_')[1].split('.')[0]} - layer_type: {layer_type}")

    # Check if a valid template layer was found
    template_fields = []
    template_field_types = []
    template_field_lenghts = []
    
    if template_layer and template_layer.isValid():
        # Extract all field name, type and len from the template layer
        for field in template_layer.fields():
            template_fields.append((field.name(),field.type(),field.length()))
        if debug: print(f"template_fields : {template_fields}")
    else:
        if debug: print(f"WARNING: No valid template found for the specified layer type. {layer_type}")

    # Start editing the target layer
    layer.startEditing()

    # Iterate over the fields to be added
    for field_name in field_names:

        # Convert field name to lower case for case insensitive comparison
        lower_field_name = field_name.lower()

        # Get a list of existing field names in lower case
        lower_existing_field_names = [field.name().lower() for field in layer.fields()]

        # if the field is missing, add it
        if lower_field_name not in lower_existing_field_names:
            if debug: print(f"lower_field_name {lower_field_name} is not in lower_existing_field_names {lower_existing_field_names}")
            new_name = lower_field_name
            new_type = QVariant.String
            new_len = 100            
            for template_field in template_fields:
                # get the field type and len from the templates, if available
                if template_field[0].lower() == lower_field_name:
                    if debug: print(f"field found in template")
                    new_type = template_field[1]
                    new_len = template_field[2]   
                    break
            if debug: print(f"new_name {new_name} new_type {new_type} new_len {new_len}")
            # QVariant.String QVariant.Int QVariant.Double

            # Create a new field
            new_field = QgsField(new_name, new_type, len=new_len)

            # Add the field to the provider
            layer.dataProvider().addAttributes([new_field])
            if debug: print(f"Added field '{new_name}' of type '{new_type}' with length {new_len}")
        else: # if the field already exists, skip it
            # if debug: print(f"Skipped field {field_name} - already exists")
            continue
    
    # Update the layer's fields
    layer.updateFields()
    # Commit changes
    layer.commitChanges()
    if debug: print("Fields added successfully and changes committed.")

def remove_fields(field_names, layer, debug=False):
    if debug: print(f"Removing virtual fields: {field_names}")
    indices_to_delete = [layer.fields().indexFromName(fieldName) for fieldName in field_names]
    if debug: print(f"indices_to_delete {indices_to_delete}")
    
    # Check if there are any fields to delete
    if indices_to_delete:
        # Start an edit session
        layer.startEditing()
        
        # Delete the fields by their indices
        if layer.deleteAttributes(indices_to_delete):
            # If the deletion was successful, commit the changes
            layer.commitChanges()
            if debug: print("Fields deleted successfully.")
        else:
            # If the deletion failed for some reason, roll back any changes
            layer.rollBack()
            if debug: print("Failed to delete fields.")
        layer.updateFields()  # Update the fields list in the layer
    else:
        if debug: print("No fields found matching the names provided.")   

def find_field(layer, name):
    """Finds a field index by name in a layer (case insensitive)."""
    name_lower = name.lower()
    for field in layer.fields():
        if field.name().lower() == name_lower:
            return layer.fields().indexFromName(field.name())
    return -1  # Field not found


def remove_all_fields_but(layer,fields_to_keep):
    layer.startEditing()
    fields = layer.fields()
    for field in fields:
        if field.name().lower() not in fields_to_keep:
            layer.deleteAttribute(fields.indexFromName(field.name()))
    layer.commitChanges()

def remove_fid_field(layer):
    layer.startEditing()
    fields = layer.fields()
    for field in fields:
        if field.name().lower() == 'fid':
            layer.deleteAttribute(fields.indexFromName(field.name()))
    layer.commitChanges()




# FILES
def delete_by_extension(path,extension,debug=False):
    if not path or not extension:
        return

    search_string = '*.' + extension
    if debug: print(f"search_string {search_string}")
    for file_path in glob.glob(os.path.join(path, search_string)):
        try:
            os.remove(file_path)
            if debug: print(f"Deleted: {file_path}")
        except Exception as e:
            if debug: print(f"Failed to delete {file_path}: {e}")