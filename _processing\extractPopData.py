from qgis.PyQt.QtCore import QCoreApplication
from qgis.core import (QgsProcessing,
                       QgsFeatureSink,
                       QgsProcessingException,
                       QgsProcessingFeedback,
                       QgsProcessingAlgorithm,
                       QgsProcessingParameterFeatureSource,
                       QgsProcessingParameterFeatureSink,
                       QgsProcessingParameterNumber,
                       QgsProcessingParameterString,
                       QgsProcessingParameterBoolean,
                       QgsProcessingParameterFolderDestination,
                       QgsProcessingParameterRasterLayer,
                       QgsProcessingParameterEnum,
                       QgsCoordinateReferenceSystem,
                       QgsRasterLayer,
                       QgsDataSourceUri,
                       QgsProject,
                       QgsDistanceArea,
                       QgsWkbTypes,
                       QgsField,
                       QgsVectorFileWriter,
                       QgsProperty,
                       QgsVectorLayer,
                       QgsApplication, 
                       QgsRectangle,
                       QgsCoordinateTransform,
                       QgsGeometry,
                       QgsFeature,
                       QgsFields,
                       QgsLayerTreeLayer,
                       QgsFeatureRequest,
                       QgsRasterBandStats,
                       NULL)

import os
import time
import gc  # Garbage collection module
from qgis import processing
from qgis import os
from qgis.PyQt.QtWidgets import QAction, QMessageBox, QFileDialog
from qgis.utils import iface
from qgis.PyQt.QtCore import QVariant
from osgeo import ogr

# IMPORT COMMON SCRIPTS
import importlib.util
import sys
import inspect

# Get the directory of the current script
try:
    # Try to get the file path from the current frame
    current_file = inspect.getfile(inspect.currentframe())
    script_dir = os.path.dirname(current_file)
except:
    # Fallback: assume we're in the _processing directory
    script_dir = os.getcwd()

module_path = os.path.join(script_dir, 'common.py')
spec = importlib.util.spec_from_file_location("common", module_path)
common = importlib.util.module_from_spec(spec)
sys.modules["common"] = common
spec.loader.exec_module(common)


class extractPopData(QgsProcessingAlgorithm):

    countries = ['Other', 'Switzerland']
    # countries = ['Switzerland','Germany', 'Other']
    MASK = 'MASK'
    FP = 'FP'
    COUNTRY = 'COUNTRY'
    DEST = 'DEST'
    DEBUG = 'DEBUG'
    GHS = 'GHS'
    THRESHOLD = 'THRESHOLD'  # Added threshold parameter
    
    def tr(self, string):
        return QCoreApplication.translate('Processing', string)
    def createInstance(self):
        return extractPopData()
    def name(self):
        return 'extractPopData'
    def displayName(self):
        return self.tr('Extract Population Data')
    def group(self):
        return self.tr('Jedsy scripts')
    def groupId(self):
        return 'jedsyscripts'
    message = (
        "Select the flight path layer, and make sure that the Volumes layer has been updated (run export all files script)\n"
        "v05"
    )
    def shortHelpString(self):
        return self.tr(self.message)
    def flags(self):
        return QgsProcessingAlgorithm.FlagNoThreading
    
    def findGHSL(self, debug=False):
    
        # Hardcoded group name
        group_name = "GHS_POP_E2025_GLOBE_R2023A_4326_3ss_V1_resampled_200x200"
        
        # Access the root of the layer tree
        root = QgsProject.instance().layerTreeRoot()
        
        # Find the group by name
        group = root.findGroup(group_name)
        if group is None:
            if debug:
                print(f"Group '{group_name}' not found")
            return [], []
            
        # Collect all layers and their modified names within the group
        layers = []
        layer_names = []
        for child in group.children():
            if isinstance(child, QgsLayerTreeLayer):
                layer = child.layer()
                layers.append(layer)
                layer_names.append(layer.name())
                if debug:
                    #print(f"Found layer: {layer.name()} -> {short_name}")
                    print(f"Found layer: {layer.name()}")
        return layers, layer_names

        
    def initAlgorithm(self, config=None):
    
        # set input layer default
        layer = iface.activeLayer()
        
        # set mask default
        if layer and type(layer) == QgsVectorLayer:
            destPath = os.path.dirname(layer.dataProvider().dataSourceUri())
            opNum = os.path.basename(destPath).split('_')[0]
            if any(layer.name() == opNum +'_Volumes' for layer in QgsProject.instance().mapLayers().values()):
                defaultMask = QgsProject.instance().mapLayersByName(opNum +'_Volumes')[0]
            else:
                defaultMask = None
        else:
            destPath = os.path.dirname(QgsProject.instance().fileName())
            defaultMask = None
        
        layersPopSource, layersPopSourceNames = self.findGHSL(debug = False)

        self.addParameter(
            QgsProcessingParameterFeatureSource(
                self.FP,
                self.tr('Input FLIGHT PATH vector layer'),
                [QgsProcessing.TypeVectorAnyGeometry]
                # defaultValue = defaultFp
            )
        )
        self.addParameter(
            QgsProcessingParameterFeatureSource(
                self.MASK,
                self.tr('Input VOLUMES vector layer'),
                [QgsProcessing.TypeVectorAnyGeometry],
                defaultValue = defaultMask
            )
        )
        self.addParameter(
            QgsProcessingParameterEnum(
                name = self.COUNTRY,
                description='Country',
                options = self.countries,
                defaultValue = self.countries[0]
            )
        )
        self.addParameter(
            QgsProcessingParameterEnum(
                name = self.GHS,
                description='If other, select the desired GHSL layer',
                options = layersPopSourceNames,
                defaultValue = len(layersPopSourceNames)-1,
                optional=True
            )
        )
        
        self.addParameter(
            QgsProcessingParameterEnum(
                name=self.THRESHOLD,
                description='Select population Threshold (disabled for CH)',
                options=[
                    'SORA 2.0 sparsely populated - 300 ppl/sqKm',
                    'SORA 2.5 SAIL 2 - 500 ppl/sqKm',
                    'SORA 2.5 SAIL 3 - 5000 ppl/sqKm'
                ],
                defaultValue=0,
                optional=False  # Make it mandatory to select an option
            )
        )
        self.addParameter(
            QgsProcessingParameterFolderDestination(
                name = self.DEST,
                description='destination folder',
                defaultValue = destPath
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                name = self.DEBUG,
                description='Debug mode',
                defaultValue = bool(0)
            )
        )
        
    def processAlgorithm(self, parameters, context, feedback):
        
        def findField(layer, name):  # Find field by name (case insensitive)
            for field in layer.fields():
                if field.name().lower() == name.lower():
                    return layer.fields().indexFromName(field.name())
            return -1  # Returns -1 if the field is not found
        def findLayerByName(layerName):
            src = QgsProject.instance().mapLayersByName(layerName)
            if len(src) != 0:
                return src[0]
            else:
                feedback.pushInfo(f"WARNING: Layer: {layerName} not found")

        def calculate_ellipsoidal_area(feature):
            d = QgsDistanceArea()
            d.setEllipsoid(QgsProject.instance().crs().ellipsoidAcronym())
            d.setSourceCrs(QgsProject.instance().crs(), QgsProject.instance().transformContext())
            if feature.geometry().isGeosValid() and feature.geometry().type() == QgsWkbTypes.PolygonGeometry:
                return d.measureArea(feature.geometry())
            else:
                return None
        
        def make_boundingbox(extents,layername='BoundingBox'):
            # Create the bounding box geometry with expanded extents
            rect = QgsGeometry.fromRect(extents)

            # Create a new memory layer to display the bounding box
            bbox_layer = QgsVectorLayer('Polygon?crs=' + mask.crs().toWkt(), layername, 'memory')
            provider = bbox_layer.dataProvider()

            # Add a field to store the extent string (optional)
            fields = QgsFields()
            fields.append(QgsField('extent', QVariant.String))
            provider.addAttributes(fields)
            bbox_layer.updateFields()

            # Create a feature with the bounding box geometry
            bbox_feature = QgsFeature()
            bbox_feature.setGeometry(rect)
            bbox_feature.setAttributes([extent_string])
            provider.addFeatures([bbox_feature])

            # Add the layer to the QGIS project
            QgsProject.instance().addMapLayer(bbox_layer)
            
            return bbox_layer

        class CustomFeedback(QgsProcessingFeedback):
            def __init__(self):
                super().__init__()
                self.messages = []
            
            def pushInfo(self, info):
                self.messages.append(info)
                super().pushInfo(info)
            
            def pushCommandInfo(self, info):
                self.messages.append(info)
                super().pushCommandInfo(info)
            
            def pushDebugInfo(self, info):
                self.messages.append(info)
                super().pushDebugInfo(info)
            
            def pushWarning(self, warning, title='', ignoreCancel=False):
                self.messages.append(warning)
                super().pushWarning(warning, title, ignoreCancel)
            
            def reportError(self, error, fatalError=False):
                self.messages.append(error)
                super().reportError(error, fatalError)


        def extract_wms_by_bb(layer, mask, outputStr, debug=False):
            import os
            from qgis.core import QgsProcessingException
            from qgis import processing

            if debug:
                print("Starting extract_wms_by_bb...")

            # Validate inputs
            if not isinstance(layer, QgsRasterLayer):
                raise QgsProcessingException("The 'layer' parameter must be a QgsRasterLayer.")
            if not isinstance(mask, QgsVectorLayer):
                raise QgsProcessingException("The 'mask' parameter must be a QgsVectorLayer.")

            # Ensure output directory exists
            output_dir = os.path.dirname(outputStr)
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            if not os.access(output_dir, os.W_OK):
                raise QgsProcessingException(f"The output directory '{output_dir}' is not writable.")

            # Prepare parameters for gdal:warpreproject
            params = {
                'INPUT': layer.source(),
                'SOURCE_CRS': layer.crs().authid(),
                'TARGET_CRS': layer.crs().authid(),
                'RESAMPLING': 0,  # Nearest neighbor
                'NODATA': None,
                'TARGET_RESOLUTION': None,
                'OPTIONS': 'COMPRESS=LZW',
                'DATA_TYPE': 0,
                'TARGET_EXTENT': None,
                'TARGET_EXTENT_CRS': None,
                'CUTLINE': mask,  # Use CUTLINE instead of MASK
                'CUTLINE_LAYER': None,
                'CUTLINE_FIELDS': [],
                'MULTITHREADING': False,
                'CROP_TO_CUTLINE': True,
                'KEEP_RESOLUTION': True,
                'OUTPUT': outputStr
            }
            if debug:
                print(f"Parameters for gdal:warpreproject: {params}")

            # Use the custom feedback class
            feedback = CustomFeedback()
            try:
                result = processing.run("gdal:warpreproject", params, feedback=feedback)
                if debug:
                    print(f"Processing result: {result}")
                    for message in feedback.messages:
                        print(message)
            except Exception as e:
                if debug:
                    print(f"Error during processing: {e}")
                raise QgsProcessingException(f"Error during processing: {e}")

            # Check if output file was created
            if not os.path.exists(outputStr):
                raise QgsProcessingException(f"Output file '{outputStr}' was not created.")

            return outputStr


        # Ensure the file is not locked by other processes before attempting to delete it
        def safe_remove(filepath, retries=3, wait=1):
            import time
            for attempt in range(retries):
                try:
                    os.remove(filepath)
                    if debug: print(f"File {filepath} successfully removed on attempt {attempt + 1}")
                    return
                except PermissionError:
                    if debug: print(f"Attempt {attempt + 1}: PermissionError, waiting to retry...")
                    time.sleep(wait)
            raise QgsProcessingException(
                f"ERROR: Permission error on {filepath}\n"
                f"Failed after {retries} attempts\n"
                "This usually happens every other time that this script is executed.\n"
                "Please try running the script again or restart QGIS."
            )

        def snap_extent_to_grid(extent, pixel_x, pixel_y):
            """Snaps extent to the nearest pixel-aligned position and ensures exact divisibility."""

            # Snap min values to the nearest lower multiple of pixel size
            snapped_x_min = (extent.xMinimum() // pixel_x) * pixel_x
            snapped_y_min = (extent.yMinimum() // pixel_y) * pixel_y

            # Compute how many pixels fit in the range and round it to avoid floating precision issues
            num_pixels_x = round((extent.xMaximum() - snapped_x_min) / pixel_x)
            num_pixels_y = round((extent.yMaximum() - snapped_y_min) / pixel_y)

            # Compute the new max values based on integer pixel steps
            snapped_x_max = snapped_x_min + (num_pixels_x * pixel_x)
            snapped_y_max = snapped_y_min + (num_pixels_y * pixel_y)

            return QgsRectangle(snapped_x_min, snapped_y_min, snapped_x_max, snapped_y_max)
        
        def remove_layer(layer_name, debug = False):
            # determine output file path based on layer name
            file_path = destpath + '/' + layer_name + '.gpkg'
            
            # Check if the layer exists and remove it from the project
            existing_layers = QgsProject.instance().mapLayersByName(layer_name)
            for existing_layer in existing_layers:
                if debug: print(f"Removing existing layer {existing_layer.name()}")
                QgsProject.instance().removeMapLayer(existing_layer.id())
            
            # delete the file
            if os.path.exists(file_path): safe_remove(file_path)
            return file_path

        

        
        ##############################################
        # -------- CONFIG --------
        ##############################################
                
        debug = self.parameterAsBoolean(parameters, self.DEBUG, context)
        mask = self.parameterAsVectorLayer(parameters,self.MASK,context)
        destpath = self.parameterAsString(parameters, self.DEST, context)
        opNum = os.path.basename(destpath).split('_')[0]
        if debug: print(f"destpath = {destpath}\nopNum = {opNum}")
        projectpath = os.path.dirname(QgsProject.instance().fileName())
        stylespath = os.path.join(projectpath, '_common')
        root = QgsProject.instance().layerTreeRoot()
        sora_option = self.parameterAsEnum(parameters, self.THRESHOLD, context)
        layers_temporary = []
        
        # Set the threshold value based on the selected option - note that this is overwritten later for CH in COUNTRY-BASED SETTINGS
        if sora_option == 0:  # "SORA 2.0 populated - 300 ppl/sqKm"
            threshold = 12
        elif sora_option == 1:  # "SORA 2.5 SAIL 2 - 500 ppl/sqKm"
            threshold = 20
        elif sora_option == 2:  # "SORA 2.5 SAIL 3 - 5000 ppl/sqKm"
            threshold = 200

        # threshold = self.parameterAsDouble(parameters, self.THRESHOLD, context)  # Get threshold value        
        if debug: print(f"sora_option {sora_option}\nthreshold {threshold}")

        fp_layer = self.parameterAsVectorLayer(parameters, self.FP, context)
        if fp_layer.name().split('_')[1] != "fp":
            raise QgsProcessingException(f"{fp_layer} is not a fp layer, determined by its name")        
        
        # COUNTRY-BASED SETTINGS
        country_selected = self.countries[parameters['COUNTRY']] # selected country in string format
        if debug: print(f"parameters['COUNTRY'] = {parameters['COUNTRY']}")
        if debug: print(f"country_selected = {country_selected}")

        if country_selected == 'Switzerland':
            popLayer = findLayerByName('SORA Bodenrisiko')
            popLayerId = popLayer.id()
            interval = 55
            featureNum = 4
            threshold = 240
        elif country_selected == 'Germany':
            popLayerId = findLayerByName('settlements_Wohngrundstücke').id()
            interval = 60
            featureNum = 3
        elif country_selected == 'Other':
            # Find GHSL layers
            if parameters['GHS'] == NULL :
                raise QgsProcessingException('ERROR: If the country is "Other" you must select a GHSL layer')
            layersPopSource, layersPopSourceNames = self.findGHSL(debug = debug)        
            popLayer = layersPopSource[parameters['GHS']]
            if not isinstance(popLayer, QgsRasterLayer):
                raise QgsProcessingException("Selected GHSL layer is not a valid raster layer.")
            popLayerId = popLayer.id()        
        if debug: print(f"SET SOURCE LAYER AND CONTOURING PARAMETERS \npopLayer = {popLayer} \npopLayerId = {popLayerId}\ninterval {interval if 'interval' in locals() else 'N/A'}\nfeatureNum {featureNum if 'featureNum' in locals() else 'N/A'}")        

        # SETUP LAYERS
        layerNames = [
            opNum + '_population_grb', #0
            opNum + '_population_fp', #1
            opNum + '_population_raw', #2
            opNum + '_population_clipped', #3
            opNum + '_population_binary_raster', #4
            opNum + '_population_binary_poly', #5
            opNum + '_population_binary_poly_selected', #6
            opNum + '_population_pts', #7
            ]
        
        # remove layers if their name is in layerNames
        layers_to_remove = [layer for layer in QgsProject.instance().mapLayers().values() if layer.name() in layerNames]
        for layer in layers_to_remove:
            if debug: print(f"Removing layer {layer}")
            QgsProject.instance().removeMapLayer(layer.id())


        # CALC TOTAL GRB AREA
        if debug: print(f"CALC GRB AREA")
        # Reproject mask layer to target raster crs (EPSG:3857) for area calculation (this is intentionally hardcoded for ellipsoidal area calc)
        if mask.crs() != QgsCoordinateReferenceSystem('EPSG:3857'):
            if debug: print(f"Reprojecting mask layer {mask} to EPSG:3857 for area calculation")
            parameter = {
                'INPUT': mask,
                'TARGET_CRS': 'EPSG:3857',
                'OUTPUT': 'memory:' + mask.name() + '_3857'
            }
            mask3857 = processing.run('native:reprojectlayer', parameter)['OUTPUT']
        else:
            mask3857 = mask
        areaGRB = 0
        for feature in mask3857.getFeatures():
            if feature['type'] == 'grb':
                area = calculate_ellipsoidal_area(feature)
                if area is not None:
                    areaGRB += area
                    if debug: print(f"feature {feature} Ellipsoidal Area: {areaGRB}")
                else:
                    if debug: print(f"WARNING: feature {feature} does not have a valid polygon geometry for area calculation.")
                    feedback.pushInfo(f"WARNING: Feature ID {feature.id()} does not have a valid polygon geometry for area calculation.")



        # -------- PREPARE POP layer_pop FOR EXTRACTION --------
        layer_pop = QgsProject.instance().mapLayer(popLayerId) # the selected pop layer_pop depends on the country
        if not layer_pop:
            if debug: print('ERROR: population layer_pop not found')
            raise QgsProcessingException('ERROR: population layer_pop not found')
        layer_pop.setOpacity(1)
        layer_pop.triggerRepaint()

        # Get the raster properties
        source_crs = layer_pop.crs()
        source_extent = layer_pop.extent()
        source_pixel_x = layer_pop.rasterUnitsPerPixelX()
        source_pixel_y = layer_pop.rasterUnitsPerPixelY()
        source_size_x = layer_pop.width()
        source_size_y = layer_pop.height()
        
        if debug:
            debug_info = f"""
            DEBUG: Source Raster Information
            --------------------------------
            CRS: {source_crs.authid()}
            Extent: {source_extent.toString()}
            Pixel Size: X = {source_pixel_x}, Y = {source_pixel_y}
            Raster Size: {source_size_x} x {source_size_y}
            """
            print(debug_info)



        
        # -------- MASK --------
        # GET EXTENTS OF THE MASK
        # Expand mask extent slightly (optional, but helps prevent extent issues)
        buffer_dist = 0.004  # 500 meters buffer
        mask_expanded = mask.extent().buffered(buffer_dist)

        # Ensure CRS transformation (from mask CRS to raster CRS)
        source_crs = mask.crs()  # CRS of mask (typically EPSG:4326)
        target_crs = layer_pop.crs()  # CRS of raster (typically EPSG:3857)

        transform_context = QgsProject.instance().transformContext()
        coord_transform = QgsCoordinateTransform(source_crs, target_crs, transform_context)

        # Transform extent
        transformed_extent = coord_transform.transformBoundingBox(mask_expanded)

        # Ensure extent is snapped to the raster grid
        snapped_extent = snap_extent_to_grid(transformed_extent, source_pixel_x, source_pixel_y)

        # Construct extent string in correct CRS
        extent_string = "{:.9f},{:.9f},{:.9f},{:.9f}".format(
            snapped_extent.xMinimum(),
            snapped_extent.xMaximum(),
            snapped_extent.yMinimum(),
            snapped_extent.yMaximum()
        )

        if debug:
            print(f"Adjusted mask extent: {mask_expanded.toString()}")
            print(f"Transformed mask extent: {transformed_extent.toString()}")
            print(f"Snapped extent: {snapped_extent.toString()}")
            print(f"Extent string for GDAL: {extent_string}")
        
        
        
        
        
        '''
        # Expand extents by ~500m converted to degrees (approximate for Germany)
        buffer_lat = 0.0045  # ~500m in latitude
        buffer_lon = 0.007   # ~500m in longitude at 50°N

        mask_extent = QgsRectangle(
            mask.extent().xMinimum() - buffer_lon,
            mask.extent().yMinimum() - buffer_lat,
            mask.extent().xMaximum() + buffer_lon,
            mask.extent().yMaximum() + buffer_lat
        )

        # Snap extent to the raster grid to ensure alignment
        snapped_extent = snap_extent_to_grid(mask_extent, source_pixel_x, source_pixel_y)

        extent_string = "{:.9f},{:.9f},{:.9f},{:.9f}".format(
            snapped_extent.xMinimum(),
            snapped_extent.xMaximum(),
            snapped_extent.yMinimum(),
            snapped_extent.yMaximum()
        )

        if debug: 
            print(f"Original mask extent: {mask_extent.toString()}")
            print(f"Snapped extent: {snapped_extent.toString()}")
            print(f"Extent string for GDAL: {extent_string}")
        '''
        
        # generate bounding box layer
        # bbox_layer = make_boundingbox(mask_extent,'Mask BoundingBox')
        bbox_layer = make_boundingbox(snapped_extent,'Mask BoundingBox')
        layers_temporary.append(bbox_layer)

        crs_string = mask.crs().authid()
        result_string = f"{extent_string} [{crs_string}]"
        if debug: print(f"result_string {result_string}")

        # REPROJECT MASK TO RASTER CRS FOR CLIPPING OPERATIONS
        mask_crs = mask.crs()
        raster_crs = layer_pop.crs()
        if mask_crs != raster_crs:
            if debug: print(f"Reprojecting mask layer_pop {mask.name()} to match raster CRS {raster_crs.authid()}")
            parameter = {
                'INPUT': mask,
                'TARGET_CRS': raster_crs,
                'OUTPUT': 'memory:' + mask.name() + '_raster_crs'
            }
            mask_clipping = processing.run('native:reprojectlayer', parameter)['OUTPUT']
        else:
            mask_clipping = mask



        # -------- CLIP POP --------
        # CLIP SECTION OF THE SOURCE POP layer_pop using the extended bounding box of mask 
        if debug: print(f"\nCLIP SECTION OF THE SOURCE POP layer_pop {layer_pop} using the extended bounding box of mask {mask}")

        if country_selected != 'Other': # Switzerland
            if debug: print('country is not "Other" -> Switzerland')
                        
            # TURN THIS INTO A FUNCTION AND APPLY ELSEWHERE?
            for i in range(100):
                rawTarget = destpath + '/' + opNum + '_population_raw_' + str(i) +'.gpkg'
                if debug: print(f"Testing rawtarget, iteration: {i}")
                
                if os.path.exists(rawTarget):
                    print(f"Exists: {rawTarget}")
                    try:
                        os.remove(rawTarget)
                    except Exception as e:
                        # if debug: print(str(e))
                        if debug: print(f"File is locked")
                        continue
                    break
                else:
                    break
            if debug: print(f"Raw target: {rawTarget}")


            # Ensure rasterization will not create a zero-sized output
            if snapped_extent.width() == 0 or snapped_extent.height() == 0:
                raise QgsProcessingException(f"Invalid raster extent: {snapped_extent.toString()}. Check CRS alignment!")


            
            
            result = processing.run("native:rasterize", {
                'EXTENT': extent_string,  # Use transformed & snapped extent
                'EXTENT_BUFFER': 100,
                'TILE_SIZE': 1024,
                'MAP_UNITS_PER_PIXEL': 10,
                'MAKE_BACKGROUND_TRANSPARENT': True,
                'LAYERS': layer_pop,
                'OUTPUT': rawTarget
            })['OUTPUT']



        elif country_selected == 'Other':
            # Validate layers
            if not layer_pop or not bbox_layer:
                raise Exception("One or both required layers are missing in the QGIS project.")

            # Extract raster properties for perfect alignment
            raster_extent = layer_pop.extent()
            extent_str = f"{raster_extent.xMinimum()},{raster_extent.xMaximum()},{raster_extent.yMinimum()},{raster_extent.yMaximum()}"
            pixel_width = layer_pop.rasterUnitsPerPixelX()
            pixel_height = layer_pop.rasterUnitsPerPixelY()
            raster_width = layer_pop.width()
            raster_height = layer_pop.height()
            
            if debug:
                print(f"Raster Resolution: {pixel_width} x {pixel_height}")
                print(f"Raster Dimensions (pixels): {raster_width} x {raster_height}")
                print(f"Raster Extent: {extent_str}")

            # Step 1: Rasterize the bbox_layer layer with perfect alignment
            rasterized_mask_result = processing.run("gdal:rasterize", {
                'INPUT': bbox_layer,
                'FIELD': None,  # No attribute field needed
                'BURN': 1,  # Value to assign inside the polygon
                'USE_Z': False,
                'UNITS': 0,  # Use georeferenced coordinates
                'X_RES': pixel_width,  # Correct X resolution
                'Y_RES': pixel_height,  # Correct Y resolution
                'WIDTH': raster_width,
                'HEIGHT': raster_height,
                'EXTENT': extent_str,  # Correct extent format
                'NODATA': 0,
                'OPTIONS': '',
                'DATA_TYPE': 5,  # Integer raster
                'INIT': None,
                'INVERT': False,
                'EXTRA': '',
                'OUTPUT': 'TEMPORARY_OUTPUT'  # Output to memory
            })

            # Get the output raster path from the processing result
            rasterized_mask_path = rasterized_mask_result['OUTPUT']

            # Load the rasterized mask into QGIS
            rasterized_mask_layer = QgsRasterLayer(rasterized_mask_path, "Rasterized Mask", "gdal")

            if rasterized_mask_layer.isValid():
                QgsProject.instance().addMapLayer(rasterized_mask_layer)
                layers_temporary.append(rasterized_mask_layer)
                if debug: print("Rasterized mask layer added to QGIS project.")
            else:
                raise Exception("Failed to load rasterized mask into QGIS.")

            # Step 2: Clip the original raster using the rasterized mask
            clipped_raster_result = processing.run("gdal:rastercalculator", {
                'INPUT_A': layer_pop.source(),
                'BAND_A': 1,
                'INPUT_B': rasterized_mask_path,
                'BAND_B': 1,
                'FORMULA': "A*(B==1)",  # Keep pixels where mask = 1
                'NO_DATA': 0,  # Set NoData where the mask is 0
                'OPTIONS': '',
                'OUTPUT': 'TEMPORARY_OUTPUT'  # Store in memory
            })
            
           
            # Get the output raster path from the processing result
            result = clipped_raster_result['OUTPUT']


        # Load the clipped raster layer_pop_clipped into QGIS
        if debug: print(f"layer_pop_clipped result {result}")
        layer_pop_clipped = iface.addRasterLayer(result, layerNames[2],'gdal')        

        # layer_pop_clipped = QgsRasterLayer(result, "Clipped Raster", "gdal")
        layers_temporary.append(layer_pop_clipped)
        
        if layer_pop_clipped.isValid():
            QgsProject.instance().addMapLayer(layer_pop_clipped)
            if debug: print(f"Clipped raster layer added to QGIS project: {layer_pop_clipped}")


            if country_selected == 'Other': # don't do this in switzerland, as the raster is high res
                # ------- POINTS -------
                # POINTS FROM RASTER with POP value
                if debug: print('\nPOINTS FROM RASTER with POP value')
                
                # extract points values to a temporary layer
                result_pts = processing.run("native:pixelstopoints", {
                    'INPUT_RASTER':layer_pop_clipped,
                    'RASTER_BAND':1,
                    'FIELD_NAME':'pop',
                    #'OUTPUT': output_pts # SAVE
                    'OUTPUT': 'TEMPORARY_OUTPUT'
                    }, context=context)
                
                # Write to the desired output manually
                output_pts = os.path.join(destpath, f"{layerNames[7]}.gpkg")
                QgsVectorFileWriter.writeAsVectorFormat(result_pts['OUTPUT'], output_pts, 'utf-8', driverName='GPKG')
    
    
                '''
                # determine output path and remove layer if it already exists
                output_pts = remove_layer(layerNames[7], debug)

                layer_pop_pts_path = processing.run("native:pixelstopoints", {
                    'INPUT_RASTER':layer_pop_clipped,
                    'RASTER_BAND':1,
                    'FIELD_NAME':'pop',
                    'OUTPUT': output_pts # SAVE
                    })['OUTPUT']
                '''
                
                if debug: print(f"Saved output_pts: {output_pts}")
                feedback.pushInfo(f"Saved output_pts: {output_pts}")

                layer_pop_pts = iface.addVectorLayer(output_pts, layerNames[7], 'ogr')
                
                '''
                if debug: print(f"Saved layer_pop_pts_path: {layer_pop_pts_path}")
                feedback.pushInfo(f"Saved layer_pop_pts_path: {layer_pop_pts_path}")

                layer_pop_pts = iface.addVectorLayer(layer_pop_pts_path, layerNames[7], 'ogr')
                '''
                
                QgsProject.instance().addMapLayer(layer_pop_pts, False)
                # style
                style = os.path.join(stylespath, 'population_pts.qml')
                layer_pop_pts.loadNamedStyle(style)
                layer_pop_pts.triggerRepaint()
        else:
            raise Exception("Failed to load clipped raster into QGIS.")            


        # Get statistics of the clipped raster
        if debug: print('Getting statistics of the clipped raster')
        provider = layer_pop_clipped.dataProvider()
        stats = provider.bandStatistics(1, QgsRasterBandStats.All)
        if debug:
            print(f"Clipped raster statistics:")
            print(f"  Minimum value: {stats.minimumValue}")
            print(f"  Maximum value: {stats.maximumValue}")
            print(f"  Mean value: {stats.mean}")
            print(f"  Standard deviation: {stats.stdDev}")
        if stats.maximumValue < threshold:
            feedback.pushInfo(f"No pixels exceed the threshold of {threshold}. Maximum raster value is {stats.maximumValue}.")
            return {}


        # -------- BINARY RASTER --------
        # APPLY THRESHOLD TO CREATE BINARY RASTER
        if debug: print('Applying raster calculator to create binary raster')
        if debug: print(f"destpath: {destpath} \nopNum {opNum}")
        binary_output = destpath + '/' + layerNames[4] + '.tif'
        
        if os.path.exists(binary_output):
            safe_remove(binary_output)
        
        # Since GDAL's raster calculator uses variable names A, B, C, etc., and the inputs are files, we can specify the input raster as 'A'
        params = {
            'INPUT_A': result,
            'BAND_A': 1,
            'INPUT_B': None,
            'BAND_B': -1,
            'INPUT_C': None,
            'BAND_C': -1,
            'INPUT_D': None,
            'BAND_D': -1,
            'INPUT_E': None,
            'BAND_E': -1,
            'INPUT_F': None,
            'BAND_F': -1,
            'FORMULA': f'((A >= {threshold}) * 1)',  # Multiply by 1 to get values of 1 and 0
            'NO_DATA': None,  # Set NoData to None to use default
            'RTYPE': 5,  # Byte
            #'OPTIONS': 'COMPRESS=LZW PREDICTOR=2',  # Apply lossless LZW compression
            'OUTPUT': binary_output
        }
        if debug:
            print(f"Raster calculator parameters: {params}")
        binary_result = processing.run("gdal:rastercalculator", params)['OUTPUT']
        if debug: print(f"Binary raster saved: {binary_result}")
        if debug: layer = iface.addRasterLayer(binary_result, layerNames[4] ,'gdal')
        
        # Get statistics of the binary raster
        if debug:
            print('Getting statistics of the binary raster')
            binaryRasterLayer = QgsRasterLayer(binary_result, layerNames[4], 'gdal')
            provider = binaryRasterLayer.dataProvider()
            stats = provider.bandStatistics(1, QgsRasterBandStats.All)
            print(f"Binary raster statistics: {binaryRasterLayer}")
            print(f"  Minimum value: {stats.minimumValue}")
            print(f"  Maximum value: {stats.maximumValue}")
            print(f"  Mean value: {stats.mean}")
            print(f"  Standard deviation: {stats.stdDev}")

        
        # ------- POLYGONIZE ----------
        # POLYGONIZE BINARY RASTER
        if debug: print(f"Polygonizing binary raster {binary_result}")

        # Define output file path
        polygon_output_path = destpath + '/' + layerNames[5] + '.gpkg'
        
        # Delete the output file if it already exists
        if os.path.exists(polygon_output_path):
            safe_remove(polygon_output_path)
            #os.remove(polygon_output_path)            

        params = {
            'INPUT': binary_result,
            'BAND': 1,
            'FIELD': 'VALUE',
            'EIGHT_CONNECTEDNESS': False,
            'OUTPUT': polygon_output_path
        }
        polygon_result = processing.run("gdal:polygonize", params)['OUTPUT']
        if debug: print(f"Polygonized raster saved: {polygon_result}")
        if debug: layer = iface.addVectorLayer(polygon_result, layerNames[5] ,'ogr')

        # FIND THE CORRECT FIELD NAME
        binary_polygons_layer = QgsVectorLayer(polygon_result, 'binary_polygons', 'ogr')
        binary_polygons_layer.setProviderEncoding('UTF-8')
        binary_polygons_layer.dataProvider().setEncoding('UTF-8')
        field_names = [field.name() for field in binary_polygons_layer.fields()]
        if debug: print(f"Fields in polygonized layer: {field_names}")
        
        # Identify the field that contains the raster values
        value_field = None
        for fname in field_names:
            if fname.lower() in ['value', 'dn', 'gridcode', 'id']:
                value_field = fname
                break
        if value_field is None:
            raise QgsProcessingException("Unable to find the field containing raster values in polygonized layer.")
        if debug: print(f"Using field '{value_field}' for selecting polygons.")

        # SELECT POLYGONS WHERE VALUE == 1
        if debug: print(f'Selecting polygons where {value_field} == 1')
        expression = f'"{value_field}" = 1'
        selected_features = binary_polygons_layer.getFeatures(QgsFeatureRequest().setFilterExpression(expression))
        ids = [f.id() for f in selected_features]
        if not ids:
            if debug: print('No features found with the specified condition.')
            feedback.pushInfo('No features found where raster value exceeds the threshold.')
            return {}
        binary_polygons_layer.selectByIds(ids)

        # SAVE SELECTED FEATURES TO NEW LAYER
        if debug: print('Saving selected features to new layer')
        selected_polygons = destpath + '/' + layerNames[6] + '.gpkg'
        QgsVectorFileWriter.writeAsVectorFormat(binary_polygons_layer, selected_polygons, 'UTF-8', binary_polygons_layer.crs(), 'GPKG', onlySelected=True)
        if debug: layer = iface.addVectorLayer(selected_polygons, layerNames[6],'ogr')



        # ------- CUT GRB ----------
        # FILTER MASK LAYER TO INCLUDE ONLY 'grb' FEATURES
        if debug: print("Filtering mask layer to include only 'grb' features")
        mask_grb = processing.run("native:extractbyattribute", {
            'INPUT': mask,
            'FIELD': 'type',
            'OPERATOR': 0,  # '=' operator
            'VALUE': 'grb',
            'OUTPUT': 'memory:' + mask.name() + '_grb'
        })['OUTPUT']
        if debug:
            mask_grb_layer = QgsProject.instance().addMapLayer(mask_grb)
            mask_grb_layer.setName(mask.name() + '_maskGrb')

        # INTERSECT GRB MASK WITH POP POLYGON
        if debug: print('INTERSECT GRB MASK WITH POP POLYGON')
        output_overlap = destpath + '/' + layerNames[0] + '_overlap.gpkg'
        if os.path.exists(output_overlap):
            safe_remove(output_overlap)      


        # Load the input layer if it is a file path (string)
        if isinstance(selected_polygons, str):
            selected_polygons = QgsVectorLayer(selected_polygons, "Selected Polygons", "ogr")
            if not selected_polygons.isValid():
                raise Exception(f"Failed to load 'selected_polygons' from {selected_polygons}")

        # Validate the mask_grb layer (should already be a QgsVectorLayer)
        if not isinstance(mask_grb, QgsVectorLayer) or not mask_grb.isValid():
            raise Exception("The 'mask_grb' layer is not valid or not a QgsVectorLayer!")
        
        params = {
            'INPUT': selected_polygons,
            'OVERLAY': mask_grb,
            'OUTPUT': output_overlap
        }

        if debug:
            print(f"INPUT layer: {selected_polygons}, Valid: {selected_polygons.isValid()}")
            print(f"OVERLAY layer: {mask_grb}, Valid: {mask_grb.isValid()}")
            
        overlap_result = processing.run("native:intersection", params)['OUTPUT']
        if debug: print(f"Overlap polygons saved: {overlap_result}")

        # CALCULATE AREA OF OVERLAP POLYGONS
        if debug: print('CALCULATE AREA OF OVERLAP POLYGONS')
        overlap_layer = QgsVectorLayer(overlap_result, 'overlap_polygons', 'ogr')
        # Reproject to a projected CRS if necessary
        if overlap_layer.crs().isGeographic():
            if debug: print('Reprojecting overlap layer to EPSG:3857')
            overlap_layer = processing.run("native:reprojectlayer", {
                'INPUT': overlap_layer,
                'TARGET_CRS': QgsCoordinateReferenceSystem('EPSG:3857'),
                'OUTPUT': 'memory:'
            })['OUTPUT']

        total_area = 0
        for feature in overlap_layer.getFeatures():
            geom = feature.geometry()
            area = geom.area()  # Since the CRS is projected, area is in square meters
            total_area += area
        if debug: print(f"Total area of overlap where raster value >= {threshold} is {total_area/1000000} sqKm")
        feedback.pushInfo(f"Total area of overlap where raster value >= {threshold} is {total_area/1000000} sqKm")



        # -------- POPULATION GRB -----------
        # POPULATION GRB
        if debug: print('POPULATION GRB')
        layer = iface.addVectorLayer(overlap_result, layerNames[0],'ogr')
        if debug: print(f"loaded layer: {layer} \n from {overlap_result}")
               
        # att areaTot field. if it's missing: create it
        if debug: print('att areaTot field')
        if layer.fields().indexFromName('areaTot') == -1:
            layer.startEditing()
            layer.dataProvider().addAttributes([QgsField('areaTot', QVariant.String,len=100)])
            layer.commitChanges()

        # areaTot value
        if debug: print(f"areaTot value")
        layer.startEditing()
        for feature in layer.getFeatures():
            layer.changeAttributeValue(feature.id(),findField(layer, 'areaTot'),round(areaGRB/1000000,2))
        layer.commitChanges()
        
        # style
        style = os.path.join(stylespath, 'population_grb.qml')
        if debug: print(f"layer {layer} \n style {style}")
        layer.loadNamedStyle(style)
        layer.triggerRepaint()




        # -------- CLIP FLIGHT PATH --------
        if debug: print('CLIP FLIGHT PATH')
        
        # determine output path and remove layer if it already exists
        output_fp = remove_layer(layerNames[1], debug)


        # Create a new layer containing only features with 'status' == 'Active'
       
        if debug: print(f"Create a new layer containing only features with 'status' == 'Active' {fp_layer}")
        active_fp_layer = processing.run("native:extractbyattribute", {
            'INPUT': fp_layer,
            'FIELD': 'status',
            'OPERATOR': 0,  # 0 corresponds to '='
            'VALUE': 'Active',
            'OUTPUT': 'memory:'
        })['OUTPUT']
        
        if debug: print(f"clipping {active_fp_layer}")
        params = {
            'INPUT': active_fp_layer,
            'OVERLAY': layer,
            #'OUTPUT': output_fp
            'OUTPUT': 'TEMPORARY_OUTPUT'
        }
        


        
        #result = processing.run("native:clip", params)['OUTPUT']
        result = processing.run("native:clip", params, context=context)
        # Write to the desired output manually
        QgsVectorFileWriter.writeAsVectorFormat(result['OUTPUT'], output_fp, 'utf-8', driverName='GPKG')

        feedback.pushInfo(f"Saved population_fp: {result['OUTPUT']}")
        if debug: print(f"Saved population_fp: {result['OUTPUT']}")

        # layer = iface.addVectorLayer(result, layerNames[1], 'ogr')
        layer = iface.addVectorLayer(output_fp, layerNames[1], 'ogr')
        QgsProject.instance().addMapLayer(layer, False)
        # style
        style = os.path.join(stylespath, 'population_fp.qml')
        layer.loadNamedStyle(style)
        layer.triggerRepaint()


        # -------- CLEAN TEMPORARY LAYERS --------
        for layer in layers_temporary:
            QgsProject.instance().removeMapLayer(layer)



        return {}
