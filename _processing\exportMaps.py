from qgis.PyQt.QtCore import QCoreApplication, QSize, Qt
from qgis.core import (QgsProcessing,
                       QgsFeatureSink,
                       QgsProcessingException,
                       QgsProcessingAlgorithm,
                       QgsProcessingParameterFeatureSource,
                       QgsProcessingParameterFeatureSink,
                       QgsProcessingParameterNumber,
                       QgsProcessingParameterString,
                       QgsProcessingParameterBoolean,
                       QgsProcessingParameterFolderDestination,
                       QgsProcessingParameterMultipleLayers,
                       QgsProcessingParameterVectorLayer,
                       QgsProcessingParameterMapLayer,
                       QgsProcessingParameterEnum,
                       QgsCoordinateReferenceSystem,
                       QgsProcessingFeatureSourceDefinition,
                       QgsExpressionContext,
                       QgsExpressionContextUtils,
                       QgsWkbTypes,
                       QgsProject,
                       QgsFeature,
                       QgsField,
                       QgsVectorFileWriter,
                       QgsVectorLayer,
                       QgsFeatureRequest,
                       QgsProperty,
                       QgsExpression,
                       QgsMapSettings,
                       QgsRectangle,
                       QgsLayerTreeGroup,
                       NULL)
                       
from qgis import processing
from qgis import os
from qgis.PyQt.QtWidgets import QAction, QMessageBox, QFileDialog
from qgis.utils import iface
from qgis.PyQt.QtCore import QVariant
import layer2kmz.layer2kmz as exporter
import time
from collections import namedtuple

# IMPORT COMMON SCRIPTS
import importlib.util
import sys
module_path = os.path.join(os.path.dirname(QgsProject.instance().fileName()), '_processing', 'common.py')
spec = importlib.util.spec_from_file_location("common", module_path)
common = importlib.util.module_from_spec(spec)
sys.modules["common"] = common
spec.loader.exec_module(common)


class exportMaps(QgsProcessingAlgorithm):

    INPUT = 'INPUT'
    MAPTYPE = 'MAPTYPE'
    DEST = 'DEST'
    DEBUG = 'DEBUG'
    OVERWRITE = 'OVERWRITE'
    COUNTRY = 'COUNTRY'
    BACKLOG = 'BACKLOG'
    
    ##################
    ##### CONFIG #####
    ##################
    
    countries = ['Germany', 'Switzerland', 'Austria', 'Other']
    
    MapType = namedtuple('MapType', ['name', 'op_layers', 'other_layers', 'groups'])
    
    # changes must be propagated to map_types_names in exportRaster.py
    shared_defaults = {
        'Initial Assessment': MapType('Initial Assessment', ['Segments', 'Targets','fp'], ['OpenStreetMap','GlobalBoundaries'], []),
        'Overview': MapType('Overview', ['Volumes', 'Targets'], ['OpenStreetMap','GlobalBoundaries'], []),
        'Air Risk': MapType('Air Risk', ['Volumes', 'AirRisk', 'AirRiskAreas', 'approachLine', 'approach', 'Low'], ['OpenStreetMap','GlobalBoundaries'], []),
        'ICAO': MapType('ICAO', ['Volumes','ICAO'], ['OpenStreetMap','GlobalBoundaries'], []),
        'Population': MapType('Population', ['Volumes', 'population_fp', 'population_grb', 'population_pts'], ['OpenStreetMap','GlobalBoundaries'], ['GHS_POP_E2025_GLOBE_R2023A_4326_3ss_V1_resampled_200x200']),
        'Details': MapType('Details', ['AirRisk', 'AirRiskAreas', 'GroundRisk', 'Volumes', 'Targets', 'approachLine', 'approach', 'Low'], ['Google Satellite muted','GlobalBoundaries'], []),
        'Elevation': MapType('Elevation', ['Volumes'], ['GlobalBoundaries'], ['DEM']),
        'Mobile A': MapType('Mobile A', ['Volumes'], ['OpenStreetMap','GlobalBoundaries'], ['LTE']),
        'Mobile B': MapType('Mobile B', ['Volumes'], ['OpenStreetMap','GlobalBoundaries'], ['LTE']),
        'Mobile C': MapType('Mobile C', ['Volumes'], ['OpenStreetMap','GlobalBoundaries'], ['LTE']),
        'Mobile D': MapType('Mobile D', ['Volumes'], ['OpenStreetMap','GlobalBoundaries'], ['LTE']),
        'HIRF': MapType('HIRF', ['Volumes', 'GroundRisk', 'Targets'], ['OpenStreetMap','GlobalBoundaries'], []),
        'Targets A': MapType('Targets A', ['AirRisk', 'AirRiskAreas', 'GroundRisk', 'Volumes', 'Targets', 'approachLine', 'approach', 'Low'], ['OpenStreetMap','GlobalBoundaries'], []),
        'Targets B': MapType('Targets B', ['AirRisk', 'GroundRisk', 'Volumes', 'Targets', 'approachLine', 'approach', 'Low'], ['Google Satellite','GlobalBoundaries'], [])
    }

    map_types = {
        'Other': {
            'Initial Assessment': shared_defaults['Initial Assessment'],
            'Overview': shared_defaults['Overview'],
            'Air Risk': shared_defaults['Air Risk'],
            'ICAO': shared_defaults['ICAO'],
            'Population': shared_defaults['Population'],
            'Details': shared_defaults['Details'],
            'Elevation': shared_defaults['Elevation'],
            'Mobile A': shared_defaults['Mobile A'],
            'Mobile B': shared_defaults['Mobile B'],
            'Mobile C': shared_defaults['Mobile C'],
            'Mobile D': shared_defaults['Mobile D'],
            'HIRF': shared_defaults['HIRF'],
            'Targets A': MapType('Targets A', ['AirRisk', 'AirRiskAreas', 'GroundRisk', 'Volumes', 'Targets', 'approachLine', 'approach', 'Low'], ['OpenStreetMap','GlobalBoundaries'], []),
            'Targets B': shared_defaults['Targets B'],
        },
        'Germany': {
            'Initial Assessment': shared_defaults['Initial Assessment'],
            'Overview': shared_defaults['Overview'],
            'Air Risk': MapType('Air Risk', ['Volumes', 'AirRisk', 'approachLine', 'approach', 'Low'], ['OpenStreetMap','GlobalBoundaries'], ['Dipul Air Risk']),
            'ICAO': MapType('ICAO', ['Volumes','ICAO'], ['OpenStreetMap','GlobalBoundaries'], ['DE_icao']),
            'Population': MapType('Population', ['Volumes', 'population_fp', 'population_grb', 'population_pts'], ['OpenStreetMap','settlements_Wohngrundstücke','GlobalBoundaries'], ['GHS_POP_E2025_GLOBE_R2023A_4326_3ss_V1_resampled_200x200']),
            'Details': MapType('Details', ['AirRisk', 'GroundRisk', 'Volumes', 'Targets', 'approachLine', 'approach', 'Low'], ['Google Satellite muted','GlobalBoundaries','hirf_weatherRadar_DE'], ['Dipul Air Risk', 'Dipul Ground Risk']),
            'Elevation': shared_defaults['Elevation'],
            'Mobile A': MapType('Mobile A', ['Volumes'], ['OpenStreetMap','GlobalBoundaries'], ['mobile_DE_Tmobile']),
            'Mobile B': MapType('Mobile B', ['Volumes'], ['OpenStreetMap','GlobalBoundaries'], ['mobile_DE_vodafone']),
            'Mobile C': MapType('Mobile C', ['Volumes'], ['OpenStreetMap','GlobalBoundaries'], ['mobile_DE_O2']),
            'Mobile D': shared_defaults['Mobile D'],
            'HIRF': MapType('HIRF', ['Volumes', 'GroundRisk', 'Targets'], ['OpenStreetMap','GlobalBoundaries','hirf_weatherRadar_DE'], []),
            'Targets A': MapType('Targets A', ['AirRisk', 'GroundRisk', 'Volumes', 'Targets', 'approachLine', 'approach', 'Low'], ['OpenStreetMap','GlobalBoundaries','hirf_weatherRadar_DE'], ['Dipul Air Risk', 'Dipul Ground Risk']),
            'Targets B': shared_defaults['Targets B'],
        },
        'Switzerland': {
            'Initial Assessment': shared_defaults['Initial Assessment'],
            'Overview': shared_defaults['Overview'],
            'Air Risk': MapType('Air Risk', ['Volumes', 'AirRisk', 'approachLine', 'approach', 'Low'], ['OpenStreetMap','GlobalBoundaries'], ['CH Air Risk']),
            'ICAO': MapType('ICAO', ['Volumes'], ['OpenStreetMap','CH_ICAO_Segelflugkarte','GlobalBoundaries'], []),
            'Population': MapType('Population', ['Volumes', 'population_fp', 'population_grb', 'population_pts'], ['SORA Bodenrisiko','OpenStreetMap','GlobalBoundaries'], []),
            'Details': MapType('Details', ['AirRisk', 'GroundRisk', 'Volumes', 'Targets', 'approachLine', 'approach', 'Low'], ['Google Satellite muted','GlobalBoundaries','hirf_weatherradar_ch','DroneRestrictedAreas_Einschränkungen für Drohnen'], ['CH Nature', 'CH Military']),
            'Elevation': shared_defaults['Elevation'],
            'Mobile A': MapType('Mobile A', ['Volumes'], ['OpenStreetMap','GlobalBoundaries'], ['mobile_CH_swisscom']),
            'Mobile B': MapType('Mobile B', ['Volumes'], ['OpenStreetMap','GlobalBoundaries'], ['mobile_CH_salt']),
            'Mobile C': MapType('Mobile C', ['Volumes'], ['OpenStreetMap','GlobalBoundaries'], ['mobile_CH_sunrise']),
            'Mobile D': MapType('Mobile D', ['Volumes'], ['OpenStreetMap','GlobalBoundaries'], ['mobile_CH_fl1']),
            'HIRF': MapType('HIRF', ['Volumes', 'GroundRisk', 'Targets'], ['OpenStreetMap','GlobalBoundaries','hirf_weatherradar_ch'], []),
            'Targets A': MapType('Targets A', ['AirRisk', 'GroundRisk', 'Volumes', 'Targets', 'approachLine', 'approach', 'Low'], ['OpenStreetMap','GlobalBoundaries','DroneRestrictedAreas_Einschränkungen für Drohnen'], ['CH Nature', 'CH Military']),
            'Targets B': shared_defaults['Targets B'],
        },
        'Austria': {
            'Initial Assessment': shared_defaults['Initial Assessment'],
            'Overview': shared_defaults['Overview'],
            'Air Risk': MapType('Air Risk', ['Volumes', 'AirRisk', 'AirRiskAreas', 'approachLine', 'approach', 'Low'], ['OpenStreetMap','GlobalBoundaries'], ['AT Air Risk']),
            'ICAO': MapType('ICAO', ['Volumes'], ['OpenStreetMap','GlobalBoundaries'], ['AT ICAO']),
            'Population': MapType('Population', ['Volumes', 'population_fp', 'population_grb', 'population_pts'], ['GlobalBoundaries','OpenStreetMap'], ['GHS_POP_E2025_GLOBE_R2023A_4326_3ss_V1_resampled_200x200', 'AT Population']),
            'Details': MapType('Details', ['AirRisk', 'AirRiskAreas', 'GroundRisk', 'Volumes', 'Targets', 'approachLine', 'approach', 'Low'], ['Google Satellite muted','GlobalBoundaries','hirf_weatherradar_at'], ['AT Air Risk','AT Nature','AT Obstacles']),
            'Elevation': shared_defaults['Elevation'],
            'Mobile A': shared_defaults['Mobile A'],
            'Mobile B': shared_defaults['Mobile B'],
            'Mobile C': shared_defaults['Mobile C'],
            'Mobile D': shared_defaults['Mobile D'],
            'HIRF': MapType('HIRF', ['Volumes', 'GroundRisk', 'Targets'], ['OpenStreetMap','GlobalBoundaries','hirf_weatherradar_at'], []),
            'Targets A': MapType('Targets A', ['AirRisk', 'AirRiskAreas', 'GroundRisk', 'Volumes', 'Targets', 'approachLine', 'approach', 'Low'], ['OpenStreetMap','GlobalBoundaries','hirf_weatherradar_at'], ['AT Air Risk','AT Nature','AT Obstacles']),
            'Targets B': shared_defaults['Targets B'],
        }
    }

    #map_types_names = list(map_types['Other'].keys())  # Used for parameter enum options
    map_types_names = list(shared_defaults.keys())  # Used for parameter enum options



    message = (
        "Select one of the layers of the current operation as input, configure and run this script\n"
        "It will display all the needed layers for the map\n"
        "ver03"
    )

    def tr(self, string):
        return QCoreApplication.translate('Processing', string)

    def createInstance(self):
        return exportMaps()

    def name(self):
        return 'exportMaps'

    def displayName(self):
        return self.tr('Setup layers for raster export')

    def group(self):
        return self.tr('Jedsy scripts')

    def groupId(self):
        return 'jedsyscripts'

    def shortHelpString(self):
        return self.tr(self.message)

    def flags(self):
        return QgsProcessingAlgorithm.FlagNoThreading

    def initAlgorithm(self, config=None):

        self.addParameter(
            QgsProcessingParameterVectorLayer(
                name=self.INPUT,
                description='Operation layer'
            )
        )
        self.addParameter(
            QgsProcessingParameterEnum(
                name=self.MAPTYPE,
                description='Map type',
                options=self.map_types_names,
                defaultValue=self.map_types_names[0],                
            )
        )
        self.addParameter(
            QgsProcessingParameterEnum(
                name=self.COUNTRY,
                description='Country',
                options=self.countries,
                defaultValue=self.countries[0],
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.BACKLOG,
                self.tr('Display backlog items'),
                defaultValue=bool(0)
            )
        )
        self.addParameter(
            QgsProcessingParameterBoolean(
                self.DEBUG,
                self.tr('Debug mode'),
                defaultValue=bool(0)
            )
        )

    def processAlgorithm(self, parameters, context, feedback):
        def find_groups_by_name(group, name):
            matching_groups = []
            for child in group.children():
                if isinstance(child, QgsLayerTreeGroup):
                    if child.name() == name:
                        matching_groups.append(child)
                    matching_groups.extend(find_groups_by_name(child, name))
            return matching_groups

        def make_parent_groups_visible(layer_tree_layer):
            parent = layer_tree_layer.parent()
            while parent is not None and isinstance(parent, QgsLayerTreeGroup):
                parent.setItemVisibilityChecked(True)
                parent = parent.parent()

        project = QgsProject.instance()
        projectpath = os.path.dirname(project.fileName())
        stylespath = os.path.join(projectpath, '_common')

        layerInput = self.parameterAsLayer(parameters, self.INPUT, context)
        debug = parameters[self.DEBUG]
        map_type_id = parameters[self.MAPTYPE]
        country_id = parameters[self.COUNTRY]

        country_key = self.countries[country_id]
        map_type_name = self.map_types_names[map_type_id]
        map_type = self.map_types.get(country_key, self.map_types['Other'])[map_type_name]

        map_type_name, required_op_layers_names, required_layers_names, required_groups_names = map_type

        opNum = layerInput.name().split('_')[0]
        root = QgsProject.instance().layerTreeRoot()

        display_hirf = map_type_name == 'HIRF'
        display_backlog = parameters[self.BACKLOG]

        processing.run("script:display_groups", {
            'BACKLOG': display_backlog,
            'HIRF': display_hirf,
            'ALLSEGMENTS': False
        })

        # Optimize: map layer names to objects once
        layer_dict = {layer.name(): layer for layer in QgsProject.instance().mapLayers().values()}

        # Resolve required operation layers
        required_op_layers = []
        for lname in required_op_layers_names:
            full_name = f"{opNum}_{lname}"
            layer = layer_dict.get(full_name)
            if layer:
                required_op_layers.append(layer)
            else:
                feedback.pushInfo(f"WARNING: No operation layer named {full_name} was found")

        # Resolve required base layers
        required_layers = []
        for lname in required_layers_names:
            layer = layer_dict.get(lname)
            if layer:
                required_layers.append(layer)
            else:
                feedback.pushInfo(f"WARNING: Required layer {lname} not found")

        # Resolve group layers
        required_group_layers = []
        for group_name in required_groups_names:
            groups = find_groups_by_name(root, group_name)
            if not groups:
                feedback.pushInfo(f"WARNING: Group '{group_name}' not found")
                continue
            for group in groups:
                required_group_layers.extend([l.layer() for l in group.findLayers()])

        # Create visibility set
        required_set = set(l.id() for l in (required_op_layers + required_layers + required_group_layers))

        for layer in layer_dict.values():
            show = layer.id() in required_set
            common.toggle_layer_visibility(layer, show=show, debug=False)
            if show:
                node = root.findLayer(layer.id())
                if node:
                    make_parent_groups_visible(node)


        # STYLE VOLUMES LAYER
        layer_volumes = QgsProject.instance().mapLayersByName(f"{opNum}_Volumes")[0]
        if map_type_name == 'ICAO':
            style = os.path.join(stylespath, 'VolumesHighlight' + '.qml')
            layer_volumes.loadNamedStyle(style)
            layer_volumes.triggerRepaint()
        else: 
            common.style_layer(layer_volumes)


        canvas = iface.mapCanvas()
        if map_type_name == 'Details':
            canvas.zoomScale(10000)
            canvas.refresh()
            if debug: print(f"New map scale set to: 1:{canvas.scale()}")
        elif map_type_name == 'Population':
            canvas.zoomScale(25000)
            canvas.refresh()
            if debug: print(f"New map scale set to: 1:{canvas.scale()}")

        return {}
